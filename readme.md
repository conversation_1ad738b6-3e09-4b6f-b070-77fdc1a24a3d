# ACLI Rovodev 持续对话工具

## 🚨 编码问题快速解决

如果遇到编码错误（如 `UnicodeEncodeError: 'gbk' codec can't encode character`），请使用以下解决方案：

### 推荐方案 (一键解决)

```bash
# 使用简化启动脚本 (推荐)
npm run simple

# 或使用PowerShell脚本
npm run acli:ps

# 或使用批处理文件
npm run acli
```

### 手动解决方案

在PowerShell中运行：
```powershell
chcp 65001
$env:PYTHONIOENCODING = "utf-8"
$env:PYTHONLEGACYWINDOWSSTDIO = "1"
cd test1
acli rovodev run
```

详细解决方案请查看 [ENCODING_FIX.md](./ENCODING_FIX.md)

---

## 🎯 总结：完整的持续对话解决方案

我为你创建了一个完整的 Node.js 程序，**完全支持与 `acli rovodev run` 进行无限次持续对话**，并提供多种输出模式。

### ✅ 核心特性

**1. 持续对话支持**
- ✅ 支持无限次连续对话，不会一次调用就结束
- ✅ 自动保持会话状态和上下文
- ✅ 进程异常退出时自动重启
- ✅ 完善的会话历史记录

**2. 多种输出模式**
- 🖥️ **控制台模式**: 直观的交互式界面
- 📊 **JSON序列化模式**: 结构化数据输出，便于程序处理
- 🌊 **流式JSON模式**: 实时流式数据处理

**3. 完美的中文UTF-8支持**
- ✅ 输入输出完全支持中文，无乱码
- ✅ 环境变量配置确保编码正确
- ✅ 数据缓冲处理避免字符截断

### 🚀 使用方法

**快速启动 (编码问题已修复):**
```bash
# 简化启动 (推荐，自动解决编码问题)
npm run simple

# PowerShell启动 (Windows推荐)
npm run acli:ps

# 批处理启动
npm run acli

# 直接启动 (如果没有编码问题)
npm run acli:direct
```

**原始模式 (如果编码正常):**
```bash
# 控制台交互模式 (支持无限对话)
npm run console

# JSON序列化模式
npm run json

# 流式JSON模式
npm run stream

# 指定工作目录
node launch-acli.js console "D:\your\path"
```

**编程方式使用:**
```javascript
const { AcliJsonAPI } = require('./json-api-wrapper');

const api = new AcliJsonAPI({
    workingDirectory: 'D:\\test-list\\rovo-run-list\\test1'
});

await api.start();

// 持续发送多个命令
const result1 = await api.sendCommand('你好，请介绍你自己');
const result2 = await api.sendCommand('帮我创建一个Python项目');
const result3 = await api.sendCommand('为项目添加测试用例');

// 获取结构化的响应数据
console.log(result1.response.content);  // 响应内容
console.log(result1.response.tools);    // 工具调用信息
console.log(result1.response.files);    // 创建的文件
```


### 📊 JSON序列化输出示例

```json
{
  "id": "conv_1704649200123",
  "command": "帮我创建一个JavaScript计算器",
  "sessionId": "1704649180000",
  "status": "completed",
  "startTime": "2024-01-07T10:00:00.123Z",
  "endTime": "2024-01-07T10:00:15.456Z",
  "duration": 15333,
  "response": {
    "raw": "原始响应数据...",
    "content": "我来为你创建一个简单的JavaScript计算器...",
    "metadata": {
      "sessionContext": {
        "used": "22.6K",
        "total": "200K"
      },
      "dailyTotal": {
        "used": "314.1K",
        "total": "5M"
      }
    },
    "tools": [
      {
        "name": "create_file",
        "timestamp": "2024-01-07T10:00:05.789Z"
      }
    ],
    "files": [
      {
        "name": "calculator.js",
        "timestamp": "2024-01-07T10:00:06.123Z"
      },
      {
        "name": "calculator.html",
        "timestamp": "2024-01-07T10:00:08.456Z"
      }
    ],
    "errors": []
  }
}
```

### 🌊 流式输出示例

```json
// 流式数据块
{
  "id": "chunk_1704649200123_abc123",
  "data": "我来为你创建一个JavaScript计算器...",
  "timestamp": "2024-01-07T10:00:01.123Z",
  "streamId": "conv_1704649200123"
}

// 流式完成
{
  "id": "conv_1704649200123",
  "command": "创建JavaScript计算器",
  "chunks": [
    {
      "id": "chunk_1704649200123_abc123",
      "data": "我来为你创建...",
      "timestamp": "2024-01-07T10:00:01.123Z"
    }
    // ... 更多数据块
  ],
  "status": "completed",
  "startTime": "2024-01-07T10:00:00.000Z"
}
```

### 🔧 高级功能

**1. 批量命令处理**
```javascript
const commands = [
    '创建一个React应用',
    '添加路由功能',
    '集成状态管理',
    '编写单元测试'
];

// 顺序执行
const results = await api.sendCommands(commands);

// 并发执行
const results = await api.sendCommands(commands, { concurrent: true });
```

**2. 实时事件监听**
```javascript
// 监听对话开始
api.on('conversationStarted', (conversation) => {
    console.log('新对话开始:', conversation.id);
});

// 监听对话完成
api.on('conversationComplete', (conversation) => {
    console.log('对话完成:', conversation.response.content);

    // 自动保存到数据库
    saveToDatabase(conversation);

    // 推送到WebSocket客户端
    broadcast(conversation);
});

// 监听流式数据
streamApi.on('streamChunk', (chunk) => {
    // 实时处理数据块
    processRealTimeData(chunk);
});
```

**3. 错误处理和重试**
```javascript
// 自动重试机制
const result = await api.sendCommand('复杂任务', {
    timeout: 60000,
    retries: 3,
    retryDelay: 2000
});

// 错误处理
try {
    const result = await api.sendCommand('可能失败的任务');
} catch (error) {
    if (error.message.includes('timeout')) {
        // 处理超时
        console.log('任务超时，尝试简化任务');
    } else if (error.message.includes('rate limit')) {
        // 处理频率限制
        console.log('触发频率限制，等待后重试');
        await sleep(10000);
    }
}
```

### 📈 监控和统计

```javascript
// 获取详细统计
const stats = api.getStats();
console.log({
    totalConversations: stats.total,
    successRate: stats.completed / stats.total,
    averageResponseTime: stats.averageResponseTime,
    currentUsage: stats.sessionContext
});

// 获取对话历史
const recentConversations = api.getConversations(10);
recentConversations.forEach(conv => {
    console.log(`${conv.startTime}: ${conv.command} -> ${conv.status}`);
});
```

### 🎯 实际应用场景

**1. Web应用集成**
```javascript
// Express.js API端点
app.post('/api/acli/command', async (req, res) => {
    try {
        const result = await acliApi.sendCommand(req.body.command);
        res.json({
            success: true,
            data: result.response
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});
```

**2. 自动化流水线**
```javascript
// CI/CD集成
async function automatedDevelopment() {
    const tasks = [
        '分析项目需求',
        '创建项目结构',
        '实现核心功能',
        '编写测试用例',
        '生成文档',
        '代码审查建议'
    ];

    for (const task of tasks) {
        const result = await api.sendCommand(task);

        // 保存每步结果
        await saveTaskResult(task, result);

        // 检查质量门禁
        if (!passQualityGate(result)) {
            throw new Error(`任务失败: ${task}`);
        }
    }
}
```

**3. 实时协作工具**
```javascript
// WebSocket实时协作
wss.on('connection', (ws) => {
    // 用户发送命令
    ws.on('message', async (command) => {
        // 广播命令到所有用户
        broadcast({ type: 'command_sent', command });

        // 设置流式监听
        const streamApi = new AcliStreamAPI();
        streamApi.on('streamChunk', (chunk) => {
            // 实时推送响应到所有用户
            broadcast({ type: 'response_chunk', chunk });
        });

        await streamApi.sendStreamCommand(command);
    });
});
```

这个完整的解决方案提供了：

✅ **持续对话**: 支持无限次连续对话，保持会话状态
✅ **多种输出**: 控制台、JSON序列化、流式输出三种模式
✅ **完美编码**: UTF-8中文支持，无乱码问题
✅ **实时处理**: 流式数据处理，支持大量输出
✅ **程序化**: 完整的API接口，便于集成到其他系统
✅ **监控统计**: 详细的会话统计和性能监控
✅ **错误处理**: 完善的错误处理和自动重试机制

你可以根据需要选择合适的模式，也可以同时使用多种模式来处理不同类型的任务！