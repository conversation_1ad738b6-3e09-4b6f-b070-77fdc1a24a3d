// test-websocket.js - WebSocket连接测试

const WebSocket = require('ws');

class WebSocketTester {
    constructor(url = 'ws://localhost:3000') {
        this.url = url;
        this.ws = null;
        this.messageCount = 0;
    }

    async test() {
        console.log('🧪 WebSocket连接测试');
        console.log('=' * 30);
        console.log(`连接地址: ${this.url}`);

        try {
            await this.connect();
            await this.testMessages();
            this.disconnect();
            console.log('\n✅ 所有测试通过！');
        } catch (error) {
            console.error('\n❌ 测试失败:', error.message);
            process.exit(1);
        }
    }

    connect() {
        return new Promise((resolve, reject) => {
            console.log('\n📡 测试连接...');
            
            this.ws = new WebSocket(this.url);

            this.ws.on('open', () => {
                console.log('✅ WebSocket连接成功');
                resolve();
            });

            this.ws.on('message', (data) => {
                try {
                    const message = JSON.parse(data);
                    console.log('📨 收到消息:', message.type);
                    this.messageCount++;
                } catch (error) {
                    console.log('📨 收到原始数据:', data.toString());
                }
            });

            this.ws.on('error', (error) => {
                console.error('❌ WebSocket错误:', error.message);
                reject(error);
            });

            this.ws.on('close', () => {
                console.log('📘 WebSocket连接关闭');
            });

            // 超时处理
            setTimeout(() => {
                if (this.ws.readyState !== WebSocket.OPEN) {
                    reject(new Error('连接超时'));
                }
            }, 5000);
        });
    }

    async testMessages() {
        console.log('\n💬 测试消息发送...');
        
        const testMessages = [
            '你好',
            '请介绍一下你自己',
            '帮我创建一个JavaScript项目'
        ];

        for (let i = 0; i < testMessages.length; i++) {
            const message = testMessages[i];
            console.log(`📤 发送消息 ${i + 1}: ${message}`);
            
            this.ws.send(JSON.stringify({
                type: 'chat',
                content: message
            }));

            // 等待响应
            await this.sleep(2000);
        }

        console.log(`✅ 发送了 ${testMessages.length} 条消息`);
        console.log(`📨 收到了 ${this.messageCount} 条响应`);
    }

    disconnect() {
        if (this.ws) {
            this.ws.close();
        }
    }

    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// 运行测试
async function main() {
    const tester = new WebSocketTester();
    await tester.test();
}

if (require.main === module) {
    main().catch(error => {
        console.error('测试失败:', error);
        process.exit(1);
    });
}

module.exports = { WebSocketTester };
