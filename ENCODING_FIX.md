# ACLI Rovodev 编码问题解决方案

## 问题描述

在Windows系统上运行 `acli rovodev run` 时遇到编码错误：

```
UnicodeEncodeError: 'gbk' codec can't encode character '\u2022' in position 0: illegal multibyte sequence
```

这是因为Windows控制台默认使用GBK编码，而rovodev输出包含Unicode字符导致的。

## 解决方案

### 方案1: 使用简化启动脚本 (推荐)

```bash
npm run simple
```

这个脚本会自动设置正确的环境变量来解决编码问题。

### 方案2: 使用批处理文件

```bash
npm run acli
```

或者直接运行：

```bash
start-acli.bat
```

### 方案3: 手动设置环境变量

在PowerShell中运行：

```powershell
# 设置控制台编码为UTF-8
chcp 65001

# 设置环境变量
$env:PYTHONIOENCODING = "utf-8"
$env:PYTHONLEGACYWINDOWSSTDIO = "1"
$env:LANG = "C.UTF-8"
$env:LC_ALL = "C.UTF-8"

# 进入工作目录并启动
cd test1
acli rovodev run
```

### 方案4: 使用编码修复工具

```bash
npm run fix
```

## 环境变量说明

- `PYTHONIOENCODING=utf-8`: 强制Python使用UTF-8编码
- `PYTHONLEGACYWINDOWSSTDIO=1`: 启用Python的Windows标准IO兼容模式
- `LANG=C.UTF-8`: 设置系统语言环境为UTF-8
- `LC_ALL=C.UTF-8`: 设置所有本地化设置为UTF-8
- `CHCP=65001`: Windows代码页设置为UTF-8

## 可用的npm脚本

- `npm run simple` - 简化启动脚本 (推荐)
- `npm run acli` - 使用批处理文件启动
- `npm run acli:direct` - 直接启动acli
- `npm run fix` - 编码修复工具
- `npm run console` - 原始的控制台模式
- `npm run json` - JSON模式
- `npm run stream` - 流式模式

## 故障排除

### 如果仍然遇到编码问题：

1. **确保使用PowerShell而不是CMD**
2. **手动设置代码页**：
   ```powershell
   chcp 65001
   ```
3. **检查acli版本**：
   ```bash
   acli --version
   ```
4. **尝试英文环境**：
   ```bash
   set LANG=en_US.UTF-8
   ```

### 如果acli命令不可用：

1. 确保已安装Atlassian CLI
2. 检查PATH环境变量
3. 重新安装acli

## 测试

运行以下命令测试编码是否正常：

```bash
npm run simple
```

如果看到正常的欢迎信息而没有编码错误，说明问题已解决。
