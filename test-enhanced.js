// test-enhanced.js - 测试增强版交互脚本

const { AcliInteractive } = require('./acli-rovodev-interactive-enhanced');

console.log('🧪 测试 ACLI Rovodev 增强版交互脚本');
console.log('=' * 50);

async function testEnhancedScript() {
    try {
        // 设置环境变量
        process.env.PYTHONIOENCODING = 'utf-8';
        process.env.PYTHONLEGACYWINDOWSSTDIO = '1';
        process.env.LANG = 'C.UTF-8';
        process.env.LC_ALL = 'C.UTF-8';
        process.env.CHCP = '65001';

        console.log('✅ 环境变量已设置');
        console.log('  PYTHONIOENCODING:', process.env.PYTHONIOENCODING);
        console.log('  PYTHONLEGACYWINDOWSSTDIO:', process.env.PYTHONLEGACYWINDOWSSTDIO);
        console.log('  LANG:', process.env.LANG);
        console.log('  LC_ALL:', process.env.LC_ALL);

        // 创建实例
        const interactive = new AcliInteractive({
            workingDirectory: './test1',
            outputMode: 'console',
            responseTimeout: 5000
        });

        console.log('\n✅ AcliInteractive 实例创建成功');

        // 测试事件监听
        interactive.on('processStarted', () => {
            console.log('✅ 进程启动事件触发');
        });

        interactive.on('commandSent', (data) => {
            console.log('✅ 命令发送事件触发:', data.command);
        });

        interactive.on('responseComplete', (data) => {
            console.log('✅ 响应完成事件触发');
            // 测试完成后退出
            setTimeout(() => {
                console.log('\n🎯 测试完成，正在退出...');
                interactive.cleanup();
            }, 2000);
        });

        console.log('\n🚀 启动 ACLI 进程...');
        console.log('💡 这将测试编码是否正常工作');
        
        // 启动进程
        await interactive.start();

    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        console.log('\n🔍 可能的原因:');
        console.log('1. ACLI 未安装或不在 PATH 中');
        console.log('2. 工作目录权限问题');
        console.log('3. 编码设置仍有问题');
        
        process.exit(1);
    }
}

// 处理退出信号
process.on('SIGINT', () => {
    console.log('\n👋 接收到退出信号');
    process.exit(0);
});

// 运行测试
testEnhancedScript();
