{"name": "acli-rovodev-interactive", "version": "1.0.0", "description": "Node.js interactive wrapper for acli rovodev run command with Chinese UTF-8 support", "main": "acli-rovodev-interactive.js", "scripts": {"start": "node launch-acli.js console", "console": "node launch-acli.js console", "json": "node launch-acli.js json", "stream": "node launch-acli.js stream", "demo": "node server.js --demo", "demo:json": "node complete-usage-example.js json", "demo:stream": "node complete-usage-example.js stream", "demo:hybrid": "node complete-usage-example.js hybrid", "test": "node test-runner.js", "test:encoding": "node test-encoding.js", "test:enhanced": "node test-enhanced.js", "verify": "node verify-enhanced.js", "test:websocket": "node test-websocket.js", "fix": "node fix-encoding.js", "acli": "start-acli.bat", "acli:ps": "powershell -ExecutionPolicy Bypass -File start-acli.ps1", "acli:direct": "cd test1 && acli rovodev run", "simple": "node simple-start.js", "working": "node working-solution.js", "server": "node server.js", "web": "node server.js"}, "keywords": ["acli", "rovodev", "interactive", "cli", "utf8", "chinese"], "author": "Your Name", "license": "MIT", "engines": {"node": ">=14.0.0"}, "dependencies": {"express": "^4.18.2", "ws": "^8.14.2"}, "devDependencies": {}, "repository": {"type": "git", "url": "https://github.com/yourusername/acli-rovodev-interactive.git"}, "config": {"workingDirectory": "D:\\test-list\\rovo-run-list\\test1", "responseTimeout": 3000, "encoding": "utf8"}}