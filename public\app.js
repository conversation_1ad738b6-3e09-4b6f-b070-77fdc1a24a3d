// app.js - ACLI聊天界面前端逻辑

class AcliChatClient {
    constructor() {
        this.ws = null;
        this.isConnected = false;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 1000;
        this.messageHistory = [];
        this.settings = this.loadSettings();
        
        this.initializeElements();
        this.bindEvents();
        this.connect();
        this.applySettings();
    }

    // 初始化DOM元素
    initializeElements() {
        this.elements = {
            chatMessages: document.getElementById('chatMessages'),
            messageInput: document.getElementById('messageInput'),
            sendBtn: document.getElementById('sendBtn'),
            statusIndicator: document.getElementById('statusIndicator'),
            statusText: document.getElementById('statusText'),
            restartBtn: document.getElementById('restartBtn'),
            clearBtn: document.getElementById('clearBtn'),
            settingsBtn: document.getElementById('settingsBtn'),
            settingsPanel: document.getElementById('settingsPanel'),
            closeSettingsBtn: document.getElementById('closeSettingsBtn'),
            connectionToast: document.getElementById('connectionToast'),
            charCount: document.getElementById('charCount'),
            autoScrollToggle: document.getElementById('autoScrollToggle'),
            timestampToggle: document.getElementById('timestampToggle'),
            soundToggle: document.getElementById('soundToggle'),
            themeSelect: document.getElementById('themeSelect')
        };
    }

    // 绑定事件
    bindEvents() {
        // 发送消息
        this.elements.sendBtn.addEventListener('click', () => this.sendMessage());
        
        // 输入框事件
        this.elements.messageInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendMessage();
            }
        });

        this.elements.messageInput.addEventListener('input', () => {
            this.updateCharCount();
            this.autoResizeTextarea();
        });

        // 按钮事件
        this.elements.restartBtn.addEventListener('click', () => this.restartAcli());
        this.elements.clearBtn.addEventListener('click', () => this.clearChat());
        this.elements.settingsBtn.addEventListener('click', () => this.toggleSettings());
        this.elements.closeSettingsBtn.addEventListener('click', () => this.toggleSettings());

        // 设置事件
        this.elements.autoScrollToggle.addEventListener('change', () => this.saveSettings());
        this.elements.timestampToggle.addEventListener('change', () => this.saveSettings());
        this.elements.soundToggle.addEventListener('change', () => this.saveSettings());
        this.elements.themeSelect.addEventListener('change', () => {
            this.saveSettings();
            this.applyTheme();
        });

        // 窗口事件
        window.addEventListener('beforeunload', () => {
            if (this.ws) {
                this.ws.close();
            }
        });
    }

    // 连接WebSocket
    connect() {
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const wsUrl = `${protocol}//${window.location.host}`;
        
        console.log('连接到:', wsUrl);
        this.updateStatus('connecting', '连接中...');

        try {
            this.ws = new WebSocket(wsUrl);
            this.setupWebSocketEvents();
        } catch (error) {
            console.error('WebSocket连接失败:', error);
            this.handleConnectionError();
        }
    }

    // 设置WebSocket事件
    setupWebSocketEvents() {
        this.ws.onopen = () => {
            console.log('WebSocket连接成功');
            this.isConnected = true;
            this.reconnectAttempts = 0;
            this.updateStatus('connected', '已连接');
            this.hideConnectionToast();
            this.enableInput();
        };

        this.ws.onmessage = (event) => {
            try {
                const data = JSON.parse(event.data);
                this.handleMessage(data);
            } catch (error) {
                console.error('解析消息失败:', error);
            }
        };

        this.ws.onclose = () => {
            console.log('WebSocket连接关闭');
            this.isConnected = false;
            this.updateStatus('disconnected', '连接断开');
            this.disableInput();
            this.showConnectionToast();
            this.attemptReconnect();
        };

        this.ws.onerror = (error) => {
            console.error('WebSocket错误:', error);
            this.handleConnectionError();
        };
    }

    // 处理接收到的消息
    handleMessage(data) {
        console.log('收到消息:', data);

        switch (data.type) {
            case 'connected':
                this.addSystemMessage('已连接到ACLI服务器');
                if (data.history && data.history.length > 0) {
                    this.loadMessageHistory(data.history);
                }
                break;

            case 'acli_ready':
                this.addSystemMessage('ACLI已准备就绪，可以开始对话');
                this.removeWelcomeMessage();
                break;

            case 'acli_response':
                this.addAssistantMessage(data.content, data.timestamp);
                this.playNotificationSound();
                break;

            case 'user_message':
                // 用户消息已在发送时添加，这里忽略
                break;

            case 'error':
                this.addSystemMessage(`错误: ${data.message}`, 'error');
                break;

            case 'acli_disconnected':
            case 'acli_restarting':
                this.addSystemMessage(data.message, 'warning');
                break;

            default:
                console.log('未知消息类型:', data.type);
        }
    }

    // 发送消息
    sendMessage() {
        const message = this.elements.messageInput.value.trim();
        if (!message || !this.isConnected) return;

        // 添加用户消息到界面
        this.addUserMessage(message);
        
        // 发送到服务器
        this.ws.send(JSON.stringify({
            type: 'chat',
            content: message
        }));

        // 清空输入框
        this.elements.messageInput.value = '';
        this.updateCharCount();
        this.autoResizeTextarea();
    }

    // 添加用户消息
    addUserMessage(content) {
        this.addMessage('user', content, new Date().toISOString());
    }

    // 添加助手消息
    addAssistantMessage(content, timestamp) {
        this.addMessage('assistant', content, timestamp);
    }

    // 添加系统消息
    addSystemMessage(content, type = 'info') {
        this.addMessage('system', content, new Date().toISOString(), type);
    }

    // 添加消息到界面
    addMessage(sender, content, timestamp, messageType = 'info') {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${sender}`;
        
        const bubbleDiv = document.createElement('div');
        bubbleDiv.className = `message-bubble ${messageType}`;
        bubbleDiv.textContent = content;
        
        messageDiv.appendChild(bubbleDiv);
        
        if (this.settings.showTimestamp) {
            const timestampDiv = document.createElement('div');
            timestampDiv.className = 'message-timestamp';
            timestampDiv.textContent = this.formatTimestamp(timestamp);
            messageDiv.appendChild(timestampDiv);
        }
        
        this.elements.chatMessages.appendChild(messageDiv);
        
        if (this.settings.autoScroll) {
            this.scrollToBottom();
        }
        
        this.messageHistory.push({ sender, content, timestamp, messageType });
    }

    // 移除欢迎消息
    removeWelcomeMessage() {
        const welcomeMessage = this.elements.chatMessages.querySelector('.welcome-message');
        if (welcomeMessage) {
            welcomeMessage.remove();
        }
    }

    // 加载消息历史
    loadMessageHistory(history) {
        this.removeWelcomeMessage();
        history.forEach(msg => {
            if (msg.type === 'user_message') {
                this.addUserMessage(msg.content);
            } else if (msg.type === 'acli_response') {
                this.addAssistantMessage(msg.content, msg.timestamp);
            }
        });
    }

    // 更新状态
    updateStatus(status, text) {
        this.elements.statusIndicator.className = `status-indicator ${status}`;
        this.elements.statusText.textContent = text;
    }

    // 启用输入
    enableInput() {
        this.elements.messageInput.disabled = false;
        this.elements.sendBtn.disabled = false;
        this.elements.messageInput.placeholder = '输入你的问题...';
    }

    // 禁用输入
    disableInput() {
        this.elements.messageInput.disabled = true;
        this.elements.sendBtn.disabled = true;
        this.elements.messageInput.placeholder = '连接断开，无法发送消息';
    }

    // 显示连接提示
    showConnectionToast() {
        this.elements.connectionToast.classList.add('show');
    }

    // 隐藏连接提示
    hideConnectionToast() {
        this.elements.connectionToast.classList.remove('show');
    }

    // 尝试重连
    attemptReconnect() {
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
            this.reconnectAttempts++;
            console.log(`尝试重连 (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
            
            setTimeout(() => {
                this.connect();
            }, this.reconnectDelay * this.reconnectAttempts);
        } else {
            this.addSystemMessage('连接失败，请刷新页面重试', 'error');
        }
    }

    // 处理连接错误
    handleConnectionError() {
        this.updateStatus('disconnected', '连接失败');
        this.disableInput();
        this.showConnectionToast();
    }

    // 重启ACLI
    restartAcli() {
        if (this.isConnected) {
            this.ws.send(JSON.stringify({ type: 'restart' }));
            this.addSystemMessage('正在重启ACLI...', 'warning');
        }
    }

    // 清空聊天
    clearChat() {
        this.elements.chatMessages.innerHTML = '';
        this.messageHistory = [];
        this.addSystemMessage('聊天记录已清空');
    }

    // 切换设置面板
    toggleSettings() {
        this.elements.settingsPanel.classList.toggle('open');
    }

    // 更新字符计数
    updateCharCount() {
        const count = this.elements.messageInput.value.length;
        this.elements.charCount.textContent = `${count}/1000`;
        
        if (count > 1000) {
            this.elements.charCount.style.color = 'var(--danger-color)';
        } else {
            this.elements.charCount.style.color = 'var(--text-muted)';
        }
    }

    // 自动调整文本框高度
    autoResizeTextarea() {
        const textarea = this.elements.messageInput;
        textarea.style.height = 'auto';
        textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';
    }

    // 滚动到底部
    scrollToBottom() {
        this.elements.chatMessages.scrollTop = this.elements.chatMessages.scrollHeight;
    }

    // 播放通知声音
    playNotificationSound() {
        if (this.settings.soundEnabled) {
            // 创建简单的提示音
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();
            
            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);
            
            oscillator.frequency.value = 800;
            gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.1);
            
            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + 0.1);
        }
    }

    // 格式化时间戳
    formatTimestamp(timestamp) {
        const date = new Date(timestamp);
        return date.toLocaleTimeString('zh-CN', { 
            hour: '2-digit', 
            minute: '2-digit' 
        });
    }

    // 加载设置
    loadSettings() {
        const defaultSettings = {
            autoScroll: true,
            showTimestamp: true,
            soundEnabled: false,
            theme: 'auto'
        };
        
        try {
            const saved = localStorage.getItem('acli-chat-settings');
            return saved ? { ...defaultSettings, ...JSON.parse(saved) } : defaultSettings;
        } catch {
            return defaultSettings;
        }
    }

    // 保存设置
    saveSettings() {
        this.settings = {
            autoScroll: this.elements.autoScrollToggle.checked,
            showTimestamp: this.elements.timestampToggle.checked,
            soundEnabled: this.elements.soundToggle.checked,
            theme: this.elements.themeSelect.value
        };
        
        localStorage.setItem('acli-chat-settings', JSON.stringify(this.settings));
    }

    // 应用设置
    applySettings() {
        this.elements.autoScrollToggle.checked = this.settings.autoScroll;
        this.elements.timestampToggle.checked = this.settings.showTimestamp;
        this.elements.soundToggle.checked = this.settings.soundEnabled;
        this.elements.themeSelect.value = this.settings.theme;
        
        this.applyTheme();
    }

    // 应用主题
    applyTheme() {
        const theme = this.settings.theme;
        document.body.setAttribute('data-theme', theme);
    }
}

// 初始化聊天客户端
document.addEventListener('DOMContentLoaded', () => {
    window.chatClient = new AcliChatClient();
});
