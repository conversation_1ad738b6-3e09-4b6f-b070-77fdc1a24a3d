# ACLI PowerShell启动器
Write-Host "🔧 设置编码环境..." -ForegroundColor Green

# 设置控制台编码
chcp 65001 | Out-Null

# 设置PowerShell编码
$OutputEncoding = [System.Text.Encoding]::UTF8
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

# 设置Python环境变量
$env:PYTHONIOENCODING = "utf-8"
$env:PYTHONUTF8 = "1"
$env:PYTHONLEGACYWINDOWSSTDIO = "1"
$env:PYTHONUNBUFFERED = "1"

# 设置系统编码
$env:LANG = "C.UTF-8"
$env:LC_ALL = "C.UTF-8"
$env:LC_CTYPE = "C.UTF-8"

# 禁用Rich库特殊字符
$env:FORCE_COLOR = "0"
$env:NO_COLOR = "1"
$env:TERM = "dumb"

# 切换目录
Set-Location "D:\test-list\rovo-run-list\test1"

Write-Host "🤖 启动ACLI Rovodev..." -ForegroundColor Green
Write-Host ""

# 启动ACLI
try {
    & acli rovodev run
} catch {
    Write-Host "❌ 启动失败: $_" -ForegroundColor Red
}

Write-Host ""
Write-Host "📘 ACLI已退出" -ForegroundColor Blue
Read-Host "按Enter键退出"