const { spawn } = require('child_process');
const readline = require('readline');
const EventEmitter = require('events');

class AcliInteractive extends EventEmitter {
    constructor(options = {}) {
        super();

        this.workingDirectory = options.workingDirectory || 'D:\\test-list\\rovo-run-list\\test1';
        this.process = null;
        this.isProcessing = false;
        this.responseBuffer = '';
        this.responseTimeout = null;
        this.responseTimeoutMs = options.responseTimeout || 5000;
        this.outputMode = options.outputMode || 'console'; // 'console', 'json', 'stream'
        this.sessionId = Date.now().toString();
        this.conversationHistory = [];
        this.currentConversation = null;

        // 创建readline接口用于用户输入
        this.rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout
        });

        // 响应解析模式
        this.responsePatterns = {
            sessionInfo: /Session context:\s*▮*\s*([\d.]+K?)\/(\d+K?)/,
            dailyTotal: /Daily total:\s*▮*\s*([\d.]+[KM]?)\/(\d+[KM]?)/,
            promptReady: />\s*$/,
            responseStart: /╭─ Response ─+╮/,
            responseEnd: /╰─+╯/,
            toolCall: /└── Calling (\w+):/,
            fileCreated: /Successfully created (.+)\./,
            error: /Error executing tool/
        };
    }

    // 设置Windows控制台编码
    async setWindowsEncoding() {
        if (process.platform === 'win32') {
            try {
                const { spawn } = require('child_process');
                await new Promise((resolve) => {
                    const chcpProcess = spawn('chcp', ['65001'], {
                        stdio: 'ignore',
                        shell: true
                    });
                    chcpProcess.on('close', (code) => {
                        if (code === 0) {
                            this.log('info', '✅ Windows控制台编码已设置为UTF-8');
                        }
                        resolve();
                    });
                    chcpProcess.on('error', () => resolve()); // 忽略错误继续
                });
            } catch (error) {
                this.log('warn', '设置控制台编码失败，但继续执行');
            }
        }
    }

    // 启动acli进程 - 支持持续对话
    async start() {
        this.log('info', '正在启动 acli rovodev run (持续对话模式)...');
        this.log('info', `工作目录: ${this.workingDirectory}`);

        // 首先设置Windows编码
        await this.setWindowsEncoding();

        try {
            // 使用包装脚本启动以解决编码问题
            const isWindows = process.platform === 'win32';
            const fs = require('fs');
            const path = require('path');

            let command, args;

            if (isWindows) {
                // 检查包装脚本是否存在
                const wrapperPath = path.join(__dirname, 'acli-wrapper.bat');
                if (fs.existsSync(wrapperPath)) {
                    command = wrapperPath;
                    args = ['rovodev', 'run'];
                    this.log('info', '使用编码包装脚本启动');
                } else {
                    // 回退到直接命令
                    command = 'cmd';
                    args = ['/c', 'chcp 65001 >nul 2>&1 && set PYTHONIOENCODING=utf-8 && set PYTHONLEGACYWINDOWSSTDIO=1 && acli rovodev run'];
                    this.log('info', '使用命令行包装启动');
                }
            } else {
                command = 'acli';
                args = ['rovodev', 'run'];
            }

            this.process = spawn(command, args, {
                cwd: this.workingDirectory,
                stdio: ['pipe', 'pipe', 'pipe'],
                encoding: 'utf8',
                shell: isWindows,
                env: {
                    ...process.env,
                    // 强制使用UTF-8编码
                    PYTHONIOENCODING: 'utf-8',
                    PYTHONLEGACYWINDOWSSTDIO: '1',
                    // 使用英文环境避免中文编码问题
                    LANG: 'C.UTF-8',
                    LC_ALL: 'C.UTF-8'
                }
            });

            // 监听标准输出 - 持续监听
            this.process.stdout.on('data', (data) => {
                this.handleOutput(data);
            });

            this.process.stderr.on('data', (data) => {
                this.log('error', '错误输出', { error: data.toString('utf8') });
            });

            // 进程意外退出时自动重启
            this.process.on('close', (code) => {
                this.log('warn', `进程退出，退出码: ${code}`);
                if (code !== 0) {
                    this.log('info', '尝试重新启动进程...');
                    setTimeout(() => this.start(), 2000);
                } else {
                    this.cleanup();
                }
            });

            this.process.on('error', (error) => {
                this.log('error', '进程启动失败', { error: error.message });
                setTimeout(() => this.start(), 5000); // 5秒后重试
            });

            this.log('success', 'acli 进程已启动，等待初始化...');
            this.emit('processStarted');

            // 等待初始化
            await this.sleep(3000);

            // 开始持续交互循环
            this.startPersistentInteraction();

        } catch (error) {
            this.log('error', '启动失败', { error: error.message });
            throw error;
        }
    }

    // 处理输出数据 - 增强版本支持JSON序列化
    handleOutput(data) {
        const output = data.toString('utf8');
        this.responseBuffer += output;

        // 根据输出模式处理数据
        switch (this.outputMode) {
            case 'console':
                this.handleConsoleOutput(output);
                break;
            case 'json':
                this.handleJsonOutput(output);
                break;
            case 'stream':
                this.handleStreamOutput(output);
                break;
        }

        // 检测响应完成
        this.detectResponseCompletion();
        this.resetResponseTimeout();
    }

    // 控制台输出模式
    handleConsoleOutput(output) {
        process.stdout.write(output);
    }

    // JSON序列化输出模式
    handleJsonOutput(output) {
        const parsedData = this.parseResponseToJson(output);
        if (parsedData) {
            console.log(JSON.stringify(parsedData, null, 2));
        }
    }

    // 流式JSON输出模式
    handleStreamOutput(output) {
        const chunks = this.parseStreamChunks(output);
        chunks.forEach(chunk => {
            const jsonChunk = {
                sessionId: this.sessionId,
                timestamp: new Date().toISOString(),
                type: 'stream_chunk',
                data: chunk,
                conversationId: this.currentConversation?.id
            };
            process.stdout.write(JSON.stringify(jsonChunk) + '\n');
        });
    }

    // 解析响应为JSON格式
    parseResponseToJson(output) {
        const lines = output.split('\n');
        let jsonData = null;

        lines.forEach(line => {
            // 检测会话信息
            const sessionMatch = line.match(this.responsePatterns.sessionInfo);
            if (sessionMatch) {
                jsonData = {
                    ...jsonData,
                    sessionInfo: {
                        used: sessionMatch[1],
                        total: sessionMatch[2],
                        timestamp: new Date().toISOString()
                    }
                };
            }

            // 检测每日统计
            const dailyMatch = line.match(this.responsePatterns.dailyTotal);
            if (dailyMatch) {
                jsonData = {
                    ...jsonData,
                    dailyTotal: {
                        used: dailyMatch[1],
                        total: dailyMatch[2],
                        timestamp: new Date().toISOString()
                    }
                };
            }

            // 检测工具调用
            const toolMatch = line.match(this.responsePatterns.toolCall);
            if (toolMatch) {
                jsonData = {
                    ...jsonData,
                    toolCall: {
                        tool: toolMatch[1],
                        timestamp: new Date().toISOString()
                    }
                };
            }

            // 检测文件创建
            const fileMatch = line.match(this.responsePatterns.fileCreated);
            if (fileMatch) {
                jsonData = {
                    ...jsonData,
                    fileCreated: {
                        filename: fileMatch[1],
                        timestamp: new Date().toISOString()
                    }
                };
            }
        });

        return jsonData;
    }

    // 解析流式数据块
    parseStreamChunks(output) {
        return output.split('\n').filter(line => line.trim() !== '');
    }

    // 检测响应完成
    detectResponseCompletion() {
        const buffer = this.responseBuffer;

        // 多种检测方式
        const isComplete =
            this.responsePatterns.promptReady.test(buffer) ||
            (buffer.includes('Session context:') && buffer.includes('Daily total:')) ||
            buffer.includes('Type "/" for available commands.') ||
            buffer.endsWith('> ');

        if (isComplete && this.isProcessing) {
            this.onResponseComplete();
        }
    }

    // 响应完成处理 - 支持持续对话
    onResponseComplete() {
        this.isProcessing = false;

        // 保存本次对话到历史记录
        if (this.currentConversation) {
            this.currentConversation.response = this.responseBuffer;
            this.currentConversation.endTime = new Date().toISOString();
            this.currentConversation.duration = Date.now() - new Date(this.currentConversation.startTime).getTime();
            this.conversationHistory.push(this.currentConversation);
        }

        // 发送完成事件
        this.emit('responseComplete', {
            conversationId: this.currentConversation?.id,
            response: this.responseBuffer,
            sessionId: this.sessionId
        });

        // 输出完成信息
        this.log('success', '响应完成', {
            conversationCount: this.conversationHistory.length,
            responseLength: this.responseBuffer.length
        });

        // 清空缓冲区准备下次对话
        this.responseBuffer = '';
        this.currentConversation = null;

        // 继续等待下一次输入 - 持续对话的关键
        this.waitForNextInput();
    }

    // 开始持续交互 - 这是持续对话的核心
    startPersistentInteraction() {
        this.log('success', '🚀 持续对话模式已启动！');
        this.log('info', '💡 提示: 输入 "exit" 退出，输入 "stats" 查看统计，输入 "history" 查看历史');
        this.waitForNextInput();
    }

    // 等待下一次输入 - 支持无限次对话
    waitForNextInput() {
        const prompt = this.conversationHistory.length === 0 ?
            '🎯 请输入你的第一个问题: ' :
            `📝 对话 #${this.conversationHistory.length + 1}: `;

        this.rl.question(prompt, (input) => {
            this.handleUserInput(input.trim());
        });
    }

    // 处理用户输入 - 增强命令支持
    handleUserInput(input) {
        // 特殊命令处理
        switch (input.toLowerCase()) {
            case 'exit':
                this.log('info', '正在退出...');
                this.cleanup();
                return;
            case 'stats':
                this.showStats();
                this.waitForNextInput();
                return;
            case 'history':
                this.showHistory();
                this.waitForNextInput();
                return;
            case 'clear':
                console.clear();
                this.waitForNextInput();
                return;
            case 'mode':
                this.switchOutputMode();
                this.waitForNextInput();
                return;
        }

        if (input === '') {
            this.log('warn', '请输入有效命令');
            this.waitForNextInput();
            return;
        }

        if (this.isProcessing) {
            this.log('warn', '⏳ 正在处理上一个请求，请稍等...');
            this.waitForNextInput();
            return;
        }

        // 发送命令 - 支持持续对话
        this.sendPersistentCommand(input);
    }

    // 发送持续命令
    sendPersistentCommand(command) {
        if (!this.process || this.process.killed) {
            this.log('error', '进程未运行，尝试重启...');
            this.start().then(() => {
                setTimeout(() => this.sendPersistentCommand(command), 2000);
            });
            return;
        }

        // 创建新的对话记录
        this.currentConversation = {
            id: `conv_${Date.now()}`,
            command: command,
            startTime: new Date().toISOString(),
            sessionId: this.sessionId
        };

        this.log('info', `📤 发送命令 #${this.conversationHistory.length + 1}`, { command });

        this.isProcessing = true;

        try {
            const commandBuffer = Buffer.from(command + '\n', 'utf8');
            this.process.stdin.write(commandBuffer);

            this.emit('commandSent', {
                command,
                conversationId: this.currentConversation.id,
                sessionId: this.sessionId
            });

            this.resetResponseTimeout();

        } catch (error) {
            this.log('error', '发送命令失败', { error: error.message });
            this.isProcessing = false;
            this.waitForNextInput();
        }
    }

    // 显示统计信息
    showStats() {
        const stats = {
            sessionId: this.sessionId,
            totalConversations: this.conversationHistory.length,
            processStatus: this.process && !this.process.killed ? '运行中' : '已停止',
            workingDirectory: this.workingDirectory,
            outputMode: this.outputMode,
            averageResponseTime: this.getAverageResponseTime(),
            uptime: this.getUptime()
        };

        console.log('\n📊 会话统计信息:');
        console.log(JSON.stringify(stats, null, 2));
    }

    // 显示对话历史
    showHistory() {
        console.log('\n📚 对话历史:');
        this.conversationHistory.slice(-5).forEach((conv, index) => {
            console.log(`\n对话 #${this.conversationHistory.length - 4 + index}:`);
            console.log(`时间: ${conv.startTime}`);
            console.log(`命令: ${conv.command}`);
            console.log(`耗时: ${conv.duration}ms`);
        });

        if (this.conversationHistory.length > 5) {
            console.log(`\n... 还有 ${this.conversationHistory.length - 5} 条历史记录`);
        }
    }

    // 切换输出模式
    switchOutputMode() {
        const modes = ['console', 'json', 'stream'];
        const currentIndex = modes.indexOf(this.outputMode);
        const nextIndex = (currentIndex + 1) % modes.length;
        this.outputMode = modes[nextIndex];

        this.log('info', `输出模式已切换为: ${this.outputMode}`);
    }

    // 重置响应超时定时器
    resetResponseTimeout() {
        if (this.responseTimeout) {
            clearTimeout(this.responseTimeout);
        }

        this.responseTimeout = setTimeout(() => {
            if (this.isProcessing) {
                this.log('warn', '响应超时，可能需要手动干预');
                this.onResponseComplete();
            }
        }, this.responseTimeoutMs);
    }

    // 获取平均响应时间
    getAverageResponseTime() {
        if (this.conversationHistory.length === 0) return 0;

        const totalTime = this.conversationHistory.reduce((sum, conv) => sum + (conv.duration || 0), 0);
        return Math.round(totalTime / this.conversationHistory.length);
    }

    // 获取运行时间
    getUptime() {
        return Date.now() - parseInt(this.sessionId);
    }

    // 统一日志输出
    log(level, message, data = {}) {
        const logEntry = {
            timestamp: new Date().toISOString(),
            level,
            message,
            sessionId: this.sessionId,
            ...data
        };

        if (this.outputMode === 'json' || this.outputMode === 'stream') {
            console.error(JSON.stringify(logEntry));
        } else {
            const prefix = {
                info: '📘',
                success: '✅',
                warn: '⚠️',
                error: '❌'
            }[level] || '📝';

            console.log(`${prefix} ${message}`, Object.keys(data).length > 0 ? data : '');
        }
    }

    // 清理资源
    cleanup() {
        if (this.responseTimeout) {
            clearTimeout(this.responseTimeout);
        }

        if (this.process && !this.process.killed) {
            this.process.kill();
        }

        this.rl.close();

        this.log('info', '会话结束', {
            totalConversations: this.conversationHistory.length,
            sessionDuration: this.getUptime()
        });

        this.emit('sessionEnded');
        process.exit(0);
    }

    // 工具函数
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// 使用示例
async function main() {
    // 设置环境变量以解决编码问题
    process.env.PYTHONIOENCODING = 'utf-8';
    process.env.PYTHONLEGACYWINDOWSSTDIO = '1';
    process.env.LANG = 'C.UTF-8';
    process.env.LC_ALL = 'C.UTF-8';
    process.env.CHCP = '65001';

    const options = {
        workingDirectory: process.argv[2] || 'D:\\test-list\\rovo-run-list\\test1',
        outputMode: process.argv[3] || 'console', // console, json, stream
        responseTimeout: 5000
    };

    console.log('🤖 ACLI Rovodev 持续对话工具 (编码修复版)');
    console.log('=' * 50);
    console.log(`输出模式: ${options.outputMode}`);
    console.log(`工作目录: ${options.workingDirectory}`);
    console.log('支持无限次持续对话！\n');

    const interactive = new AcliInteractive(options);

    // 事件监听
    interactive.on('processStarted', () => {
        console.log('🚀 进程启动成功');
    });

    interactive.on('commandSent', (data) => {
        console.log('📤 命令已发送:', data.command);
    });

    interactive.on('responseComplete', (data) => {
        console.log('✅响应完成, 对话ID:', data.conversationId);
    });

    // 处理程序退出
    process.on('SIGINT', () => {
        console.log('\n接收到中断信号，正在清理...');
        interactive.cleanup();
    });

    // 启动持续对话
    await interactive.start();
}

if (require.main === module) {
    main().catch(error => {
        console.error('程序启动失败:', error);
        process.exit(1);
    });
}

module.exports = { AcliInteractive };