# PowerShell 双向通信和ACLI交互控制

这个项目展示了如何在Node.js中与PowerShell进程进行双向通信，特别是用于控制ACLI Rovodev。

## 文件说明

### 1. `working-solution.js` (已修改)
- 原始的ACLI启动脚本，现在支持双向通信
- 使用 `stdio: ['pipe', 'pipe', 'pipe']` 替代 `stdio: 'inherit'`
- 可以监听输出并发送命令

### 2. `interactive-powershell.js`
- 通用的PowerShell交互式控制器
- 提供命令行界面与PowerShell交互
- 支持实时输入输出

### 3. `acli-interactive.js`
- 专门用于ACLI Rovodev的控制器类
- 自动设置环境变量和编码
- 提供ACLI状态检测和自动化响应

### 4. `usage-example.js`
- 使用示例，展示如何使用ACLI控制器
- 支持交互式和自动化两种模式

## 核心概念

### 双向通信设置

```javascript
const psProcess = spawn('powershell', [
    '-ExecutionPolicy', 'Bypass',
    '-Command', psCommand
], {
    stdio: ['pipe', 'pipe', 'pipe'], // 关键：使用管道
    cwd: workingDir,
    encoding: 'utf8'
});
```

### 监听数据返回

```javascript
// 监听标准输出
psProcess.stdout.on('data', (data) => {
    const output = data.toString('utf8');
    console.log('📤 PowerShell输出:', output);
    
    // 处理特定输出
    if (output.includes('特定关键词')) {
        // 执行相应操作
        sendCommand('响应命令');
    }
});

// 监听错误输出
psProcess.stderr.on('data', (data) => {
    const error = data.toString('utf8');
    console.error('❌ PowerShell错误:', error);
});
```

### 发送命令

```javascript
function sendCommand(command) {
    if (psProcess.stdin && !psProcess.stdin.destroyed) {
        console.log('📥 发送命令:', command);
        psProcess.stdin.write(command + '\n');
    } else {
        console.error('❌ 无法发送命令，进程不可用');
    }
}
```

## 使用方法

### 1. 基本双向通信测试

```bash
node working-solution.js
```

这会启动修改后的ACLI脚本，现在支持双向通信。

### 2. 通用PowerShell交互

```bash
node interactive-powershell.js
```

启动交互式PowerShell控制器，可以：
- 输入任意PowerShell命令
- 实时查看输出
- 使用内置帮助命令

### 3. ACLI专用控制器

```bash
node usage-example.js
```

启动ACLI专用控制器，支持：
- 自动环境设置
- ACLI状态监控
- 交互式命令发送

### 4. 自动化模式

```bash
node usage-example.js auto
```

运行自动化示例，展示如何：
- 自动发送命令序列
- 处理ACLI响应
- 实现无人值守操作

## 关键特性

### 1. 编码处理
- 自动设置UTF-8编码
- 处理中文字符显示问题
- 配置Python环境变量

### 2. 状态监控
- 实时检测ACLI运行状态
- 自动识别错误和异常
- 提供状态查询接口

### 3. 自动化响应
- 根据输出内容自动响应
- 支持预定义命令序列
- 可配置的响应规则

### 4. 错误处理
- 优雅的进程关闭
- 异常情况恢复
- 详细的错误日志

## 实际应用场景

### 1. ACLI自动化
```javascript
const acli = new ACLIController();
await acli.start();

// 自动选择选项
await acli.sendUserInput('1');

// 自动确认
await acli.sendUserInput('y');

// 检查状态
const status = acli.getStatus();
```

### 2. 批量操作
```javascript
const commands = ['help', '1', 'y', 'status'];
for (const cmd of commands) {
    await acli.sendUserInput(cmd);
    await new Promise(resolve => setTimeout(resolve, 2000));
}
```

### 3. 条件响应
```javascript
psProcess.stdout.on('data', (data) => {
    const output = data.toString();
    
    if (output.includes('是否继续')) {
        sendCommand('y');
    } else if (output.includes('选择选项')) {
        sendCommand('1');
    }
});
```

## 注意事项

1. **进程管理**: 确保正确关闭子进程，避免僵尸进程
2. **编码问题**: Windows环境下需要特别注意UTF-8编码设置
3. **异步处理**: 命令发送和响应处理都是异步的
4. **错误恢复**: 实现适当的错误处理和重试机制

## 故障排除

### 常见问题

1. **编码乱码**: 确保设置了正确的编码环境变量
2. **进程无响应**: 检查stdin是否已关闭
3. **命令不执行**: 验证PowerShell执行策略设置

### 调试技巧

1. 启用详细日志输出
2. 监控进程状态变化
3. 检查命令格式和语法
4. 使用超时机制避免死锁

这个解决方案提供了完整的PowerShell双向通信框架，特别适用于ACLI Rovodev的自动化控制。
