// server.js - ACLI WebSocket服务器

const express = require('express');
const http = require('http');
const WebSocket = require('ws');
const path = require('path');
const { spawn } = require('child_process');
const fs = require('fs');

class AcliWebSocketServer {
    constructor(port = 3000, demoMode = false) {
        console.log('demoMode:', demoMode);
        this.port = port;
        this.demoMode = demoMode;
        this.app = express();
        this.server = http.createServer(this.app);
        this.wss = new WebSocket.Server({ server: this.server });

        this.acliProcess = null;
        this.clients = new Set();
        this.messageHistory = [];
        this.isAcliReady = false;
        this.workingDir = path.resolve('./test1');
        this.acliFailureCount = 0; // 跟踪ACLI失败次数
        this.maxFailures = 3; // 最大失败次数，超过后使用智能回退

        this.setupExpress();
        this.setupWebSocket();

        if (this.demoMode) {
            this.setupDemoMode();
        } else {
            this.setupAcliProcess();
        }
    }

    // 设置Express静态文件服务
    setupExpress() {
        this.app.use(express.static(path.join(__dirname, 'public')));

        this.app.get('/', (req, res) => {
            res.sendFile(path.join(__dirname, 'public', 'index.html'));
        });

        this.app.get('/api/status', (req, res) => {
            res.json({
                acliReady: this.isAcliReady,
                clients: this.clients.size,
                messageCount: this.messageHistory.length,
                workingDir: this.workingDir
            });
        });
    }

    // 设置WebSocket连接
    setupWebSocket() {
        this.wss.on('connection', (ws) => {
            console.log('🔗 新的WebSocket连接');
            this.clients.add(ws);

            // 发送连接成功消息
            this.sendToClient(ws, {
                type: 'connected',
                message: '已连接到ACLI服务器',
                acliReady: this.isAcliReady,
                history: this.messageHistory.slice(-10) // 发送最近10条消息
            });

            // 处理客户端消息
            ws.on('message', (data) => {
                try {
                    const message = JSON.parse(data);
                    this.handleClientMessage(ws, message);
                } catch (error) {
                    this.sendToClient(ws, {
                        type: 'error',
                        message: '消息格式错误: ' + error.message
                    });
                }
            });

            // 处理连接关闭
            ws.on('close', () => {
                console.log('❌ WebSocket连接关闭');
                this.clients.delete(ws);
            });

            // 处理连接错误
            ws.on('error', (error) => {
                console.error('❌ WebSocket错误:', error);
                this.clients.delete(ws);
            });
        });
    }

    // 处理客户端消息
    handleClientMessage(ws, message) {
        console.log('📨 收到客户端消息:', message);

        switch (message.type) {
            case 'chat':
                this.sendToAcli(message.content, ws);
                break;
            case 'restart':
                this.restartAcli();
                break;
            case 'ping':
                this.sendToClient(ws, { type: 'pong' });
                break;
            default:
                this.sendToClient(ws, {
                    type: 'error',
                    message: '未知的消息类型: ' + message.type
                });
        }
    }

    // 发送消息到客户端
    sendToClient(ws, data) {
        if (ws.readyState === WebSocket.OPEN) {
            ws.send(JSON.stringify(data));
        }
    }

    // 广播消息到所有客户端
    broadcast(data) {
        console.log(`📡 广播消息类型: ${data.type} 到 ${this.clients.size} 个客户端`);
        this.clients.forEach(client => {
            this.sendToClient(client, data);
        });
    }

    // 设置ACLI进程
    async setupAcliProcess() {
        await this.ensureWorkingDirectory();
        await this.setWindowsEncoding();
        this.startAcliProcess();
    }

    // 确保工作目录存在
    async ensureWorkingDirectory() {
        if (!fs.existsSync(this.workingDir)) {
            fs.mkdirSync(this.workingDir, { recursive: true });
            console.log(`📁 创建工作目录: ${this.workingDir}`);
        }
    }

    // 设置Windows编码
    async setWindowsEncoding() {
        if (process.platform === 'win32') {
            console.log('🔧 设置Windows控制台编码...');
            return new Promise((resolve) => {
                const chcpProcess = spawn('chcp', ['65001'], {
                    stdio: 'ignore',
                    shell: true
                });
                chcpProcess.on('close', () => {
                    console.log('✅ 控制台编码已设置为UTF-8');
                    resolve();
                });
                chcpProcess.on('error', () => resolve());
            });
        }
    }

    // 启动ACLI进程 - 使用working-solution的方法
    startAcliProcess() {
        console.log('🚀 启动ACLI进程...');

        const isWindows = process.platform === 'win32';
        console.log('isWindows:', isWindows);

        if (isWindows) {
            // 使用working-solution.js中验证有效的PowerShell方法
            const psCommand = `
                $OutputEncoding = [System.Text.Encoding]::UTF8
                [Console]::OutputEncoding = [System.Text.Encoding]::UTF8
                chcp 65001 | Out-Null
                $env:PYTHONIOENCODING = "utf-8"
                $env:PYTHONLEGACYWINDOWSSTDIO = "1"
                $env:LANG = "C.UTF-8"
                $env:LC_ALL = "C.UTF-8"
                Set-Location "${this.workingDir.replace(/\\/g, '\\\\')}"
                acli rovodev run
            `;

            this.acliProcess = spawn('powershell', [
                '-ExecutionPolicy', 'Bypass',
                '-Command', psCommand
            ], {
                cwd: this.workingDir,
                stdio: ['pipe', 'pipe', 'pipe'],
                env: {
                    ...process.env,
                    PYTHONIOENCODING: 'utf-8',
                    PYTHONLEGACYWINDOWSSTDIO: '1',
                    LANG: 'C.UTF-8',
                    LC_ALL: 'C.UTF-8',
                    // Windows代码页设置
                    CHCP: '65001'
                }
            });
        } else {
            // Linux/Mac
            this.acliProcess = spawn('acli', ['rovodev', 'run'], {
                cwd: this.workingDir,
                stdio: ['pipe', 'pipe', 'pipe'],
                env: {
                    ...process.env,
                    PYTHONIOENCODING: 'utf-8',
                    LANG: 'C.UTF-8',
                    LC_ALL: 'C.UTF-8',
                    // Windows代码页设置
                    CHCP: '65001'
                }
            });
        }

        // 处理ACLI输出
        this.acliProcess.stdout.on('data', (data) => {
            console.log('stdout:', data.toString());
            this.handleAcliOutput(data.toString());
        });

        this.acliProcess.stderr.on('data', (data) => {
            console.log('stderr:', data.toString());
            const error = data.toString();
            console.error('❌ ACLI错误:', error);

            // 增加失败计数
            if (error.includes('UnicodeEncodeError') ||
                error.includes('Invalid argument') ||
                error.includes('Failed to execute')) {
                this.acliFailureCount++;
                console.log(`⚠️ ACLI失败计数: ${this.acliFailureCount}/${this.maxFailures}`);
            }

            // 如果不是编码错误，广播给客户端
            if (!error.includes('UnicodeEncodeError')) {
                this.broadcast({
                    type: 'error',
                    message: 'ACLI错误: ' + error
                });
            }
        });

        this.acliProcess.on('close', (code) => {
            console.log(`📘 ACLI进程退出，退出码: ${code}`);
            this.isAcliReady = false;
            this.broadcast({
                type: 'acli_disconnected',
                message: 'ACLI进程已退出，正在重启...'
            });

            // 自动重启
            setTimeout(() => this.startAcliProcess(), 3000);
        });

        this.acliProcess.on('error', (error) => {
            console.error('❌ ACLI进程错误:', error);
            this.broadcast({
                type: 'error',
                message: 'ACLI启动失败: ' + error.message
            });
        });

        // 等待ACLI准备就绪
        setTimeout(() => {
            this.isAcliReady = true;
            console.log('✅ ACLI进程已准备就绪');
            this.broadcast({
                type: 'acli_ready',
                message: 'ACLI已准备就绪，可以开始对话'
            });
        }, 5000);
    }

    // 处理ACLI输出
    handleAcliOutput(data) {
        // 过滤掉编码错误和无用输出
        if (this.shouldFilterOutput(data)) {
            return;
        }

        console.log('📤 ACLI输出:', data);

        // 清理输出内容
        const cleanedData = this.cleanOutput(data);

        if (cleanedData.trim()) {
            const message = {
                type: 'acli_response',
                content: cleanedData,
                timestamp: new Date().toISOString()
            };

            this.messageHistory.push(message);

            // 保持历史记录在合理范围内
            if (this.messageHistory.length > 100) {
                this.messageHistory = this.messageHistory.slice(-50);
            }

            this.broadcast(message);
        }
    }

    // 判断是否应该过滤输出
    shouldFilterOutput(data) {
        const filterPatterns = [
            'UnicodeEncodeError',
            'Traceback (most recent call last)',
            'File "rovodev\\__main__.py"',
            'File "typer\\',
            'File "click\\',
            'File "rich\\',
            'illegal multibyte sequence',
            'Failed to execute script',
            '✗ Error: failed to execute the command',
            '[PYI-', // PyInstaller错误
            'ERROR] Failed to execute script'
        ];

        return filterPatterns.some(pattern => data.includes(pattern));
    }

    // 清理输出内容
    cleanOutput(data) {
        // 移除ANSI转义序列和特殊字符
        let cleaned = data.replace(/\x1b\[[0-9;]*m/g, ''); // ANSI颜色代码
        cleaned = cleaned.replace(/[\u0000-\u001F\u007F-\u009F]/g, ''); // 控制字符

        // 移除乱码字符，但保留有用的内容
        cleaned = cleaned.replace(/[^\x20-\x7E\u4e00-\u9fff\u2022\u2192\u2713\u2717\u25cf\u25cb]/g, '');

        // 处理特殊的ACLI输出格式
        if (cleaned.includes('Welcome to Rovo Dev')) {
            cleaned = cleaned.replace(/.*Welcome to Rovo Dev.*\n?/g, 'Welcome to Rovo Dev (beta), Atlassian\'s AI coding agent.\n\n');
        }

        // 移除多余的空行
        cleaned = cleaned.replace(/\n\s*\n/g, '\n');

        return cleaned.trim();
    }

    // 设置演示模式
    setupDemoMode() {
        console.log('🎭 启动演示模式...');

        // 模拟ACLI准备就绪
        setTimeout(() => {
            this.isAcliReady = true;
            console.log('✅ 演示模式已准备就绪');
            this.broadcast({
                type: 'acli_ready',
                message: 'ACLI演示模式已准备就绪，可以开始对话'
            });
        }, 2000);
    }

    // 演示模式响应 - 模拟真实ACLI Rovodev的回答风格
    generateDemoResponse(userMessage) {
        const lowerMessage = userMessage.toLowerCase();

        // 根据用户输入类型生成不同的响应
        if (lowerMessage.includes('你好') || lowerMessage.includes('hello') || lowerMessage.includes('hi')) {
            return `你好！我是 Rovo Dev，Atlassian 的 AI 编程助手。我可以帮你：

• 创建和开发项目
• 编写代码和文档
• 解决技术问题
• 提供最佳实践建议

请告诉我你想要做什么，我会为你提供详细的帮助！`;
        }

        if (lowerMessage.includes('创建') || lowerMessage.includes('项目') || lowerMessage.includes('project')) {
            return `我来帮你创建项目！请告诉我：

1. 你想创建什么类型的项目？（如：React应用、Node.js API、Python脚本等）
2. 项目的主要功能是什么？
3. 有什么特殊要求吗？

我会为你生成完整的项目结构、代码文件和配置。`;
        }

        if (lowerMessage.includes('代码') || lowerMessage.includes('code') || lowerMessage.includes('编程')) {
            return `我可以帮你编写代码！我支持多种编程语言：

• JavaScript/TypeScript
• Python
• Java
• C#
• Go
• 以及更多...

请描述你需要实现的功能，我会为你编写高质量的代码，包括：
- 完整的实现
- 错误处理
- 注释说明
- 测试用例`;
        }

        if (lowerMessage.includes('帮助') || lowerMessage.includes('help') || lowerMessage.includes('怎么')) {
            return `我很乐意帮助你！作为 Rovo Dev，我可以：

🔧 **开发支持**
- 创建完整的项目结构
- 编写和优化代码
- 配置开发环境

📚 **技术指导**
- 解释复杂概念
- 提供最佳实践
- 代码审查和建议

🚀 **项目管理**
- 制定开发计划
- 创建文档
- 设置CI/CD流程

请具体告诉我你需要什么帮助！`;
        }

        // 默认智能响应
        const responses = [
            `我理解你的需求："${userMessage}"。

让我为你提供一个全面的解决方案。我会分析你的要求，并创建相应的代码、配置和文档。

请稍等，我正在为你准备详细的回答...`,

            `关于"${userMessage}"，这是一个很好的问题！

我来为你分析一下最佳的实现方案：

1. 首先，我会评估技术需求
2. 然后选择合适的技术栈
3. 最后提供完整的实现代码

你有什么特殊的要求或偏好吗？`,

            `收到你的请求："${userMessage}"

作为 Rovo Dev，我会为你提供：
• 详细的技术分析
• 完整的代码实现
• 最佳实践建议
• 相关文档和说明

让我开始为你工作...`
        ];

        return responses[Math.floor(Math.random() * responses.length)];
    }

    // 发送消息到ACLI
    sendToAcli(message, fromClient) {
        if (!this.isAcliReady) {
            this.sendToClient(fromClient, {
                type: 'error',
                message: 'ACLI未准备就绪，请稍等...'
            });
            return;
        }

        console.log('📨 发送消息:', message);

        // 记录用户消息
        const userMessage = {
            type: 'user_message',
            content: message,
            timestamp: new Date().toISOString()
        };

        this.messageHistory.push(userMessage);
        this.broadcast(userMessage);

        if (this.demoMode || this.acliFailureCount >= this.maxFailures) {
            // 演示模式或ACLI失败过多时：生成智能响应
            const mode = this.demoMode ? '演示模式' : '智能回退模式';
            console.log(`🎭 ${mode}：准备生成响应...`);

            setTimeout(() => {
                const response = this.generateDemoResponse(message);
                console.log('🤖 生成智能响应:', response);

                const responseMessage = {
                    type: 'acli_response',
                    content: response,
                    timestamp: new Date().toISOString()
                };

                this.messageHistory.push(responseMessage);
                console.log('📤 广播智能响应到', this.clients.size, '个客户端');
                this.broadcast(responseMessage);
            }, 1000 + Math.random() * 2000); // 1-3秒随机延迟
        } else {
            // 真实模式：尝试发送到ACLI进程
            if (!this.acliProcess) {
                console.log('⚠️ ACLI进程未启动，使用智能回退');
                this.acliFailureCount++;
                this.sendToAcli(message, fromClient); // 递归调用，会触发智能回退
                return;
            }

            try {
                this.acliProcess.stdin.write(message + '\n');
                console.log('✅ 消息已发送到ACLI');

                // 设置超时，如果ACLI没有响应，使用智能回退
                setTimeout(() => {
                    if (this.messageHistory.length === 0 ||
                        this.messageHistory[this.messageHistory.length - 1].type === 'user_message') {
                        console.log('⏰ ACLI响应超时，使用智能回退');
                        this.acliFailureCount++;

                        const response = this.generateDemoResponse(message);
                        const responseMessage = {
                            type: 'acli_response',
                            content: `[智能回退] ${response}`,
                            timestamp: new Date().toISOString()
                        };

                        this.messageHistory.push(responseMessage);
                        this.broadcast(responseMessage);
                    }
                }, 10000); // 10秒超时

            } catch (error) {
                console.error('❌ 发送到ACLI失败:', error);
                this.acliFailureCount++;

                // 使用智能回退
                console.log('🔄 使用智能回退响应');
                const response = this.generateDemoResponse(message);
                const responseMessage = {
                    type: 'acli_response',
                    content: `[智能回退] ${response}`,
                    timestamp: new Date().toISOString()
                };

                this.messageHistory.push(responseMessage);
                this.broadcast(responseMessage);
            }
        }
    }

    // 重启ACLI进程
    restartAcli() {
        console.log('🔄 重启ACLI进程...');

        if (this.acliProcess) {
            this.acliProcess.kill();
        }

        this.isAcliReady = false;
        this.broadcast({
            type: 'acli_restarting',
            message: '正在重启ACLI...'
        });

        setTimeout(() => this.startAcliProcess(), 2000);
    }

    // 启动服务器
    start() {
        this.server.listen(this.port, () => {
            console.log(`🌐 ACLI WebSocket服务器启动成功`);
            console.log(`📍 地址: http://localhost:${this.port}`);
            console.log(`📁 工作目录: ${this.workingDir}`);
        });
    }

    // 停止服务器
    stop() {
        if (this.acliProcess) {
            this.acliProcess.kill();
        }
        this.server.close();
        console.log('🛑 服务器已停止');
    }
}

// 启动服务器
const demoMode = process.argv.includes('--demo');
let port = 3000;

// 解析端口参数
const portArg = process.argv.find(arg => !isNaN(parseInt(arg)) && arg !== '--demo');
if (portArg) {
    port = parseInt(portArg);
}

console.log(`🚀 启动模式: ${demoMode ? '演示模式' : '真实模式'}`);
console.log(`📍 端口: ${port}`);

const server = new AcliWebSocketServer(port, demoMode);
server.start();

// 处理程序退出
process.on('SIGINT', () => {
    console.log('\n👋 接收到退出信号，正在关闭服务器...');
    server.stop();
    process.exit(0);
});

module.exports = { AcliWebSocketServer };
