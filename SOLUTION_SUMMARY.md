# ACLI Rovodev 编码问题解决方案总结

## 问题分析

你遇到的错误是典型的Windows编码问题：

```
UnicodeEncodeError: 'gbk' codec can't encode character '\u2022' in position 0: illegal multibyte sequence
```

**根本原因：**
- Windows控制台默认使用GBK编码
- Rovodev输出包含Unicode字符（如 `•` 符号）
- Python在Windows上的标准输出编码处理问题

## 解决方案

我为你创建了多个解决方案，按推荐程度排序：

### 🥇 方案1: 简化启动脚本 (最推荐)

```bash
npm run simple
```

**优点：**
- 一键启动，自动设置所有环境变量
- 跨平台兼容
- 包含完整的错误处理和提示

### 🥈 方案2: PowerShell脚本 (Windows推荐)

```bash
npm run acli:ps
```

**优点：**
- 专为Windows优化
- 彩色输出和友好提示
- 自动检查依赖

### 🥉 方案3: 批处理文件

```bash
npm run acli
```

**优点：**
- 传统Windows批处理
- 简单直接

### 🔧 方案4: 手动设置

在PowerShell中：
```powershell
chcp 65001
$env:PYTHONIOENCODING = "utf-8"
$env:PYTHONLEGACYWINDOWSSTDIO = "1"
$env:LANG = "C.UTF-8"
$env:LC_ALL = "C.UTF-8"
cd test1
acli rovodev run
```

## 可用的npm脚本

| 脚本 | 描述 | 推荐度 |
|------|------|--------|
| `npm run simple` | 简化启动脚本 | ⭐⭐⭐⭐⭐ |
| `npm run acli:ps` | PowerShell脚本 | ⭐⭐⭐⭐ |
| `npm run acli` | 批处理文件 | ⭐⭐⭐ |
| `npm run acli:direct` | 直接启动 | ⭐⭐ |
| `npm run test:encoding` | 编码测试 | 🧪 |
| `npm run fix` | 编码修复工具 | 🔧 |

## 测试和验证

运行编码测试：
```bash
npm run test:encoding
```

这会检查：
- 环境变量设置
- 工作目录
- ACLI可用性
- Unicode字符处理

## 环境变量说明

| 变量 | 值 | 作用 |
|------|----|----- |
| `PYTHONIOENCODING` | `utf-8` | 强制Python使用UTF-8编码 |
| `PYTHONLEGACYWINDOWSSTDIO` | `1` | 启用Windows标准IO兼容模式 |
| `LANG` | `C.UTF-8` | 设置系统语言环境 |
| `LC_ALL` | `C.UTF-8` | 设置所有本地化设置 |
| `CHCP` | `65001` | Windows代码页设置为UTF-8 |

## 故障排除

### 如果仍然有编码问题：

1. **确保使用PowerShell**（不是CMD）
2. **手动设置代码页**：
   ```powershell
   chcp 65001
   ```
3. **检查ACLI版本**：
   ```bash
   acli --version
   ```

### 如果ACLI命令不可用：

1. 安装Atlassian CLI
2. 检查PATH环境变量
3. 重启终端

## 文件说明

| 文件 | 用途 |
|------|------|
| `simple-start.js` | 简化启动脚本 |
| `start-acli.ps1` | PowerShell启动脚本 |
| `start-acli.bat` | 批处理启动脚本 |
| `fix-encoding.js` | 编码修复工具 |
| `test-encoding.js` | 编码测试工具 |
| `ENCODING_FIX.md` | 详细解决方案文档 |

## 下一步

1. **立即尝试**：
   ```bash
   npm run simple
   ```

2. **如果成功**：你可以开始正常使用ACLI Rovodev进行对话

3. **如果失败**：运行测试查看具体问题
   ```bash
   npm run test:encoding
   ```

4. **获取帮助**：查看 `ENCODING_FIX.md` 获取更详细的解决方案

## 总结

这个解决方案包含了：
- ✅ 多种启动方式
- ✅ 自动环境变量设置
- ✅ 完整的错误处理
- ✅ 测试和验证工具
- ✅ 详细的文档说明

选择最适合你的方案，通常 `npm run simple` 就能解决问题！
