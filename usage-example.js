// usage-example.js - 使用示例

const ACLIController = require('./acli-interactive');
const readline = require('readline');

async function interactiveExample() {
    console.log('🎮 ACLI交互式控制示例');
    console.log('=' * 40);
    
    const acli = new ACLIController();
    
    try {
        // 启动ACLI控制器
        await acli.start();
        
        // 创建命令行接口
        const rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout,
            prompt: '💻 输入命令 (help查看帮助): '
        });

        // 显示帮助信息
        function showHelp() {
            console.log('\n📖 可用命令:');
            console.log('  help          - 显示帮助');
            console.log('  status        - 查看ACLI状态');
            console.log('  restart       - 重启ACLI');
            console.log('  send <text>   - 发送文本到ACLI');
            console.log('  output        - 显示最近输出');
            console.log('  exit          - 退出程序');
            console.log('\n💡 示例:');
            console.log('  send help     - 发送help命令到ACLI');
            console.log('  send 1        - 发送选项1到ACLI');
            console.log('  send y        - 发送确认到ACLI');
            console.log('');
        }

        showHelp();

        // 处理用户输入
        rl.on('line', async (input) => {
            const command = input.trim();
            
            if (command === 'help') {
                showHelp();
            } else if (command === 'status') {
                const status = acli.getStatus();
                console.log('📊 ACLI状态:', JSON.stringify(status, null, 2));
            } else if (command === 'restart') {
                console.log('🔄 重启ACLI...');
                await acli.restartACLI();
            } else if (command.startsWith('send ')) {
                const text = command.substring(5);
                await acli.sendUserInput(text);
            } else if (command === 'output') {
                console.log('📤 最近输出:');
                console.log(acli.getRecentOutput());
            } else if (command === 'exit') {
                console.log('👋 退出程序...');
                await acli.close();
                rl.close();
                process.exit(0);
            } else if (command) {
                // 直接发送到ACLI
                await acli.sendUserInput(command);
            }
            
            rl.prompt();
        });

        rl.on('close', async () => {
            console.log('\n👋 再见！');
            await acli.close();
            process.exit(0);
        });

        rl.prompt();
        
    } catch (error) {
        console.error('❌ 示例运行失败:', error.message);
        process.exit(1);
    }
}

// 自动化示例
async function automatedExample() {
    console.log('🤖 ACLI自动化控制示例');
    console.log('=' * 40);
    
    const acli = new ACLIController();
    
    try {
        await acli.start();
        
        // 等待ACLI完全启动
        await new Promise(resolve => setTimeout(resolve, 5000));
        
        console.log('🎯 开始自动化序列...');
        
        // 自动化命令序列
        const commands = [
            'help',           // 获取帮助
            '1',              // 选择选项1（假设）
            'y',              // 确认
            'status'          // 查看状态
        ];
        
        for (let i = 0; i < commands.length; i++) {
            const cmd = commands[i];
            console.log(`\n⏳ 步骤 ${i + 1}: 发送 "${cmd}"`);
            
            await acli.sendUserInput(cmd);
            
            // 等待响应
            await new Promise(resolve => setTimeout(resolve, 3000));
            
            // 显示最近输出
            console.log('📤 响应:');
            console.log(acli.getRecentOutput(5));
        }
        
        console.log('\n✅ 自动化序列完成');
        
        // 保持运行一段时间观察
        console.log('⏰ 保持运行30秒以观察输出...');
        await new Promise(resolve => setTimeout(resolve, 30000));
        
        await acli.close();
        
    } catch (error) {
        console.error('❌ 自动化示例失败:', error.message);
        process.exit(1);
    }
}

// 主函数
async function main() {
    const args = process.argv.slice(2);
    const mode = args[0] || 'interactive';
    
    console.log('🚀 启动ACLI控制示例');
    console.log(`📋 模式: ${mode}`);
    
    if (mode === 'auto' || mode === 'automated') {
        await automatedExample();
    } else {
        await interactiveExample();
    }
}

// 处理程序退出
process.on('SIGINT', () => {
    console.log('\n👋 接收到退出信号...');
    process.exit(0);
});

process.on('uncaughtException', (error) => {
    console.error('❌ 未捕获的异常:', error.message);
    process.exit(1);
});

if (require.main === module) {
    main();
}

module.exports = {
    interactiveExample,
    automatedExample
};
