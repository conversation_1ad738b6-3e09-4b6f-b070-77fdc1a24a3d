// final-solution.js - 最终的ACLI双向通信解决方案

const { spawn } = require('child_process');
const readline = require('readline');
const path = require('path');

class ACLIManager {
    constructor() {
        this.workingDir = path.resolve('./test1');
        this.process = null;
        this.isRunning = false;
        this.rl = null;
    }

    async start() {
        console.log('🚀 ACLI管理器启动');
        console.log('=' * 40);
        console.log(`📁 工作目录: ${this.workingDir}`);
        
        // 启动ACLI进程
        await this.startACLI();
        
        // 设置交互界面
        this.setupInteraction();
        
        console.log('\n✅ ACLI管理器已准备就绪');
        console.log('💡 输入 help 查看可用命令');
    }

    async startACLI() {
        console.log('\n🤖 启动ACLI进程...');
        
        // 设置环境变量
        const env = {
            ...process.env,
            // Python编码设置
            PYTHONIOENCODING: 'utf-8',
            PYTHONUTF8: '1',
            PYTHONLEGACYWINDOWSSTDIO: '1',
            PYTHONUNBUFFERED: '1',
            // 系统编码设置
            LANG: 'C.UTF-8',
            LC_ALL: 'C.UTF-8',
            LC_CTYPE: 'C.UTF-8',
            // 禁用Rich库特殊字符（减少编码问题）
            FORCE_COLOR: '0',
            NO_COLOR: '1',
            TERM: 'dumb'
        };
        
        // 使用CMD启动ACLI（这个方法在测试中表现最好）
        this.process = spawn('cmd', ['/c', 'chcp 65001 && acli rovodev run'], {
            stdio: ['pipe', 'pipe', 'pipe'],
            cwd: this.workingDir,
            env: env,
            shell: false
        });
        
        this.setupProcessHandlers();
        
        // 等待进程启动
        await this.waitForStartup();
    }

    setupProcessHandlers() {
        // 监听标准输出
        this.process.stdout.on('data', (data) => {
            const output = data.toString('utf8');
            
            // 过滤掉一些噪音输出
            if (output.trim() && 
                !output.includes('Active code page') &&
                !output.includes('������')) {
                console.log('📤 ACLI:', output.trim());
            }
            
            // 检测ACLI状态
            this.detectACLIStatus(output);
        });
        
        // 监听标准错误
        this.process.stderr.on('data', (data) => {
            const error = data.toString('utf8');
            
            // 过滤编码错误（我们知道会有，但不影响使用）
            if (error.includes('UnicodeEncodeError')) {
                // 静默处理编码错误
                return;
            }
            
            if (error.trim() && 
                !error.includes('gbk') && 
                !error.includes('illegal multibyte sequence')) {
                console.error('❌ ACLI错误:', error.trim());
            }
        });
        
        // 监听进程关闭
        this.process.on('close', (code) => {
            console.log(`\n📘 ACLI进程退出，退出码: ${code}`);
            this.isRunning = false;
            
            if (this.rl) {
                this.rl.close();
            }
        });
        
        this.process.on('error', (error) => {
            console.error('❌ 进程错误:', error.message);
            this.isRunning = false;
        });
    }

    detectACLIStatus(output) {
        if (output.includes('Welcome to Rovo Dev') || 
            output.includes('Atlassian')) {
            console.log('✅ ACLI成功启动！');
            this.isRunning = true;
        }
        
        if (output.includes('Here are some quick tips')) {
            console.log('💡 ACLI已准备接收命令');
        }
        
        // 检测常见的交互提示
        if (output.includes('?') || 
            output.includes('Enter') || 
            output.includes('选择') ||
            output.includes('choice')) {
            console.log('🔔 ACLI正在等待输入');
        }
    }

    async waitForStartup() {
        return new Promise((resolve) => {
            const checkStartup = () => {
                if (this.isRunning) {
                    resolve();
                } else {
                    setTimeout(checkStartup, 500);
                }
            };
            
            // 最多等待10秒
            setTimeout(() => {
                if (!this.isRunning) {
                    console.log('⚠️ ACLI可能未完全启动，但继续...');
                }
                resolve();
            }, 10000);
            
            checkStartup();
        });
    }

    setupInteraction() {
        this.rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout,
            prompt: '💻 ACLI命令: '
        });

        this.rl.on('line', (input) => {
            const command = input.trim();
            
            if (command === 'help') {
                this.showHelp();
            } else if (command === 'status') {
                this.showStatus();
            } else if (command === 'restart') {
                this.restart();
            } else if (command === 'exit' || command === 'quit') {
                this.close();
            } else if (command === 'clear') {
                console.clear();
            } else if (command) {
                this.sendToACLI(command);
            }
            
            this.rl.prompt();
        });

        this.rl.on('close', () => {
            console.log('\n👋 再见！');
            this.close();
        });

        this.rl.prompt();
    }

    sendToACLI(command) {
        if (this.process && this.process.stdin && !this.process.stdin.destroyed) {
            console.log(`📥 发送: ${command}`);
            this.process.stdin.write(command + '\n');
        } else {
            console.error('❌ 无法发送命令，ACLI进程不可用');
        }
    }

    showHelp() {
        console.log('\n📖 可用命令:');
        console.log('  help     - 显示此帮助');
        console.log('  status   - 显示ACLI状态');
        console.log('  restart  - 重启ACLI');
        console.log('  clear    - 清屏');
        console.log('  exit     - 退出程序');
        console.log('  <其他>   - 发送到ACLI');
        console.log('\n💡 ACLI常用命令示例:');
        console.log('  help     - ACLI帮助');
        console.log('  1        - 选择选项1');
        console.log('  y        - 确认');
        console.log('  n        - 取消');
        console.log('');
    }

    showStatus() {
        console.log('\n📊 ACLI状态:');
        console.log(`  运行状态: ${this.isRunning ? '✅ 运行中' : '❌ 未运行'}`);
        console.log(`  进程存活: ${this.process && !this.process.killed ? '✅ 存活' : '❌ 已终止'}`);
        console.log(`  工作目录: ${this.workingDir}`);
        console.log('');
    }

    async restart() {
        console.log('🔄 重启ACLI...');
        
        if (this.process && !this.process.killed) {
            this.process.kill('SIGTERM');
        }
        
        await new Promise(resolve => setTimeout(resolve, 2000));
        await this.startACLI();
        
        console.log('✅ ACLI重启完成');
    }

    close() {
        console.log('🔄 正在关闭ACLI管理器...');
        
        if (this.rl) {
            this.rl.close();
        }
        
        if (this.process && !this.process.killed) {
            // 尝试优雅关闭
            if (this.process.stdin && !this.process.stdin.destroyed) {
                this.process.stdin.write('exit\n');
                this.process.stdin.end();
            }
            
            // 强制关闭
            setTimeout(() => {
                if (this.process && !this.process.killed) {
                    this.process.kill('SIGTERM');
                }
            }, 2000);
        }
        
        process.exit(0);
    }
}

// 主函数
async function main() {
    const manager = new ACLIManager();
    
    try {
        await manager.start();
    } catch (error) {
        console.error('❌ 启动失败:', error.message);
        process.exit(1);
    }
    
    // 处理程序退出
    process.on('SIGINT', () => {
        console.log('\n👋 接收到退出信号...');
        manager.close();
    });
}

if (require.main === module) {
    main();
}

module.exports = ACLIManager;
