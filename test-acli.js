// test-acli.js - 测试ACLI启动的最简单方案

const { spawn } = require('child_process');
const path = require('path');

console.log('🧪 ACLI启动测试器');
console.log('=' * 30);

const workingDir = path.resolve('./test1');

// 方法1: 直接使用CMD启动，设置所有必要的环境变量
function testMethod1() {
    console.log('\n📋 方法1: CMD + 环境变量');
    
    const env = {
        ...process.env,
        // Python编码设置
        PYTHONIOENCODING: 'utf-8',
        PYTHONUTF8: '1',
        PYTHONLEGACYWINDOWSSTDIO: '1',
        PYTHONUNBUFFERED: '1',
        // 系统编码设置
        LANG: 'C.UTF-8',
        LC_ALL: 'C.UTF-8',
        LC_CTYPE: 'C.UTF-8',
        // 禁用Rich库特殊字符
        FORCE_COLOR: '0',
        NO_COLOR: '1',
        TERM: 'dumb'
    };
    
    const process1 = spawn('cmd', ['/c', 'chcp 65001 && acli rovodev run'], {
        stdio: ['pipe', 'pipe', 'pipe'],
        cwd: workingDir,
        env: env,
        shell: false
    });
    
    setupProcessHandlers(process1, '方法1-CMD');
    return process1;
}

// 方法2: 使用PowerShell但简化命令
function testMethod2() {
    console.log('\n📋 方法2: PowerShell简化版');
    
    const psCommand = `
        chcp 65001 | Out-Null;
        $env:PYTHONIOENCODING = 'utf-8';
        $env:PYTHONUTF8 = '1';
        $env:PYTHONLEGACYWINDOWSSTDIO = '1';
        $env:FORCE_COLOR = '0';
        $env:NO_COLOR = '1';
        $env:TERM = 'dumb';
        Set-Location '${workingDir}';
        acli rovodev run
    `;
    
    const process2 = spawn('powershell', [
        '-ExecutionPolicy', 'Bypass',
        '-Command', psCommand
    ], {
        stdio: ['pipe', 'pipe', 'pipe'],
        cwd: workingDir
    });
    
    setupProcessHandlers(process2, '方法2-PowerShell');
    return process2;
}

// 方法3: 使用Python包装器
function testMethod3() {
    console.log('\n📋 方法3: Python包装器');
    
    const pythonCode = `
import os
import sys
import subprocess

# 设置编码
os.environ['PYTHONIOENCODING'] = 'utf-8'
os.environ['PYTHONUTF8'] = '1'
os.environ['PYTHONLEGACYWINDOWSSTDIO'] = '1'
os.environ['FORCE_COLOR'] = '0'
os.environ['NO_COLOR'] = '1'
os.environ['TERM'] = 'dumb'

# 重新配置stdout
if hasattr(sys.stdout, 'reconfigure'):
    sys.stdout.reconfigure(encoding='utf-8', errors='replace')
if hasattr(sys.stderr, 'reconfigure'):
    sys.stderr.reconfigure(encoding='utf-8', errors='replace')

print("Starting ACLI via Python wrapper...")
subprocess.run(['acli', 'rovodev', 'run'], cwd=r'${workingDir}')
`;
    
    const process3 = spawn('python', ['-c', pythonCode], {
        stdio: ['pipe', 'pipe', 'pipe'],
        cwd: workingDir
    });
    
    setupProcessHandlers(process3, '方法3-Python');
    return process3;
}

function setupProcessHandlers(proc, methodName) {
    let hasSuccessOutput = false;
    
    proc.stdout.on('data', (data) => {
        const output = data.toString('utf8');
        console.log(`📤 [${methodName}] 输出:`, output.trim());
        
        if (output.includes('Welcome to Rovo Dev') || 
            output.includes('Atlassian') ||
            output.includes('rovodev')) {
            hasSuccessOutput = true;
            console.log(`✅ [${methodName}] 检测到成功启动！`);
        }
    });
    
    proc.stderr.on('data', (data) => {
        const error = data.toString('utf8');
        
        if (error.includes('UnicodeEncodeError')) {
            console.log(`⚠️ [${methodName}] 仍有编码错误，但可能可以继续运行`);
        } else if (error.trim()) {
            console.error(`❌ [${methodName}] 错误:`, error.trim());
        }
    });
    
    proc.on('close', (code) => {
        console.log(`📘 [${methodName}] 进程退出，退出码: ${code}`);
        if (hasSuccessOutput) {
            console.log(`✅ [${methodName}] 此方法可能有效！`);
        } else {
            console.log(`❌ [${methodName}] 此方法无效`);
        }
    });
    
    proc.on('error', (error) => {
        console.error(`❌ [${methodName}] 进程错误:`, error.message);
    });
}

// 主函数
async function main() {
    const args = process.argv.slice(2);
    const method = args[0] || 'all';
    
    console.log(`🎯 测试方法: ${method}`);
    console.log(`📁 工作目录: ${workingDir}`);
    
    let processes = [];
    
    switch (method) {
        case '1':
            processes.push(testMethod1());
            break;
        case '2':
            processes.push(testMethod2());
            break;
        case '3':
            processes.push(testMethod3());
            break;
        case 'all':
        default:
            console.log('\n🔄 依次测试所有方法...');
            
            // 测试方法1
            const p1 = testMethod1();
            processes.push(p1);
            
            // 等待5秒后测试方法2
            setTimeout(() => {
                if (!p1.killed) {
                    p1.kill('SIGTERM');
                }
                const p2 = testMethod2();
                processes.push(p2);
                
                // 再等待5秒后测试方法3
                setTimeout(() => {
                    if (!p2.killed) {
                        p2.kill('SIGTERM');
                    }
                    const p3 = testMethod3();
                    processes.push(p3);
                }, 5000);
            }, 5000);
            break;
    }
    
    // 处理Ctrl+C
    process.on('SIGINT', () => {
        console.log('\n👋 接收到退出信号，关闭所有进程...');
        processes.forEach(p => {
            if (p && !p.killed) {
                p.kill('SIGTERM');
            }
        });
        process.exit(0);
    });
}

if (require.main === module) {
    main();
}

console.log('\n💡 使用方法:');
console.log('  node test-acli.js     - 测试所有方法');
console.log('  node test-acli.js 1   - 只测试CMD方法');
console.log('  node test-acli.js 2   - 只测试PowerShell方法');
console.log('  node test-acli.js 3   - 只测试Python方法');
