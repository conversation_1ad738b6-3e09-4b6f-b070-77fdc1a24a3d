# start-acli.ps1 - PowerShell启动脚本

Write-Host "🔧 ACLI Rovodev 启动脚本 (PowerShell版)" -ForegroundColor Cyan
Write-Host "==========================================" -ForegroundColor Cyan

# 设置控制台编码为UTF-8
Write-Host "📝 设置控制台编码为UTF-8..." -ForegroundColor Yellow
chcp 65001 | Out-Null

# 设置环境变量
Write-Host "🌍 设置环境变量..." -ForegroundColor Yellow
$env:PYTHONIOENCODING = "utf-8"
$env:PYTHONLEGACYWINDOWSSTDIO = "1"
$env:LANG = "C.UTF-8"
$env:LC_ALL = "C.UTF-8"

Write-Host "  PYTHONIOENCODING: $env:PYTHONIOENCODING" -ForegroundColor Gray
Write-Host "  PYTHONLEGACYWINDOWSSTDIO: $env:PYTHONLEGACYWINDOWSSTDIO" -ForegroundColor Gray
Write-Host "  LANG: $env:LANG" -ForegroundColor Gray
Write-Host "  LC_ALL: $env:LC_ALL" -ForegroundColor Gray

# 检查并创建工作目录
$workingDir = "test1"
if (-not (Test-Path $workingDir)) {
    Write-Host "📁 创建工作目录: $workingDir" -ForegroundColor Yellow
    New-Item -ItemType Directory -Path $workingDir | Out-Null
}

Write-Host "📁 工作目录: $(Resolve-Path $workingDir)" -ForegroundColor Green

# 检查acli是否可用
Write-Host "🔍 检查acli是否可用..." -ForegroundColor Yellow
try {
    $acliVersion = acli --version 2>$null
    Write-Host "✅ acli命令可用" -ForegroundColor Green
} catch {
    Write-Host "❌ acli命令不可用，请确保已安装Atlassian CLI" -ForegroundColor Red
    Write-Host "💡 安装方法: https://developer.atlassian.com/cli/" -ForegroundColor Yellow
    Read-Host "按Enter键退出"
    exit 1
}

Write-Host ""
Write-Host "🚀 启动 acli rovodev run..." -ForegroundColor Green
Write-Host "💡 提示: 按 Ctrl+C 退出" -ForegroundColor Yellow
Write-Host ""

# 进入工作目录并启动acli
try {
    Set-Location $workingDir
    acli rovodev run
} catch {
    Write-Host "❌ 启动失败: $($_.Exception.Message)" -ForegroundColor Red
} finally {
    Set-Location ..
    Write-Host ""
    Write-Host "📘 acli 进程已退出" -ForegroundColor Blue
    Read-Host "按Enter键退出"
}
