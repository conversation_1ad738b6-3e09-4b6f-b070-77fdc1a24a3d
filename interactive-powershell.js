// interactive-powershell.js - 交互式PowerShell通信示例

const { spawn } = require('child_process');
const readline = require('readline');

class PowerShellInteractive {
    constructor() {
        this.psProcess = null;
        this.isReady = false;
        this.commandQueue = [];
        this.rl = null;
    }

    async start() {
        console.log('🚀 启动交互式PowerShell...');
        
        // 启动PowerShell进程
        this.psProcess = spawn('powershell', [
            '-ExecutionPolicy', 'Bypass',
            '-NoExit',  // 保持PowerShell会话打开
            '-Command', '-'  // 从stdin读取命令
        ], {
            stdio: ['pipe', 'pipe', 'pipe'],
            shell: false
        });

        this.setupEventHandlers();
        this.setupReadline();
        
        // 等待PowerShell准备就绪
        await this.waitForReady();
        
        console.log('✅ PowerShell已准备就绪，可以开始交互');
        this.showHelp();
    }

    setupEventHandlers() {
        // 监听标准输出
        this.psProcess.stdout.on('data', (data) => {
            const output = data.toString('utf8');
            console.log('📤 PowerShell:', output.trim());
            
            // 检测PowerShell是否准备就绪
            if (output.includes('PS ') && !this.isReady) {
                this.isReady = true;
            }
            
            // 处理特定输出
            this.handleSpecificOutput(output);
        });

        // 监听标准错误
        this.psProcess.stderr.on('data', (data) => {
            const error = data.toString('utf8');
            console.error('❌ PowerShell错误:', error.trim());
        });

        // 监听进程关闭
        this.psProcess.on('close', (code) => {
            console.log(`\n📘 PowerShell进程退出，退出码: ${code}`);
            if (this.rl) {
                this.rl.close();
            }
            process.exit(code);
        });

        // 监听进程错误
        this.psProcess.on('error', (error) => {
            console.error('❌ PowerShell进程错误:', error.message);
        });
    }

    setupReadline() {
        // 设置命令行输入接口
        this.rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout,
            prompt: '💻 输入命令 (输入 help 查看帮助): '
        });

        this.rl.on('line', (input) => {
            const command = input.trim();
            
            if (command === 'help') {
                this.showHelp();
            } else if (command === 'exit' || command === 'quit') {
                this.close();
            } else if (command === 'clear') {
                console.clear();
            } else if (command.startsWith('send ')) {
                const psCommand = command.substring(5);
                this.sendCommand(psCommand);
            } else if (command) {
                this.sendCommand(command);
            }
            
            this.rl.prompt();
        });

        this.rl.on('close', () => {
            console.log('\n👋 再见！');
            this.close();
        });
    }

    async waitForReady() {
        return new Promise((resolve) => {
            const checkReady = () => {
                if (this.isReady) {
                    resolve();
                } else {
                    setTimeout(checkReady, 100);
                }
            };
            checkReady();
        });
    }

    sendCommand(command) {
        if (this.psProcess && this.psProcess.stdin && !this.psProcess.stdin.destroyed) {
            console.log(`📥 发送命令: ${command}`);
            this.psProcess.stdin.write(command + '\n');
        } else {
            console.error('❌ 无法发送命令，PowerShell进程不可用');
        }
    }

    handleSpecificOutput(output) {
        // 处理特定的输出模式
        if (output.includes('acli')) {
            console.log('🎯 检测到ACLI相关输出');
        }
        
        if (output.includes('Error') || output.includes('Exception')) {
            console.log('⚠️ 检测到错误信息');
        }
        
        if (output.includes('rovodev')) {
            console.log('🤖 检测到Rovodev相关输出');
        }
    }

    showHelp() {
        console.log('\n📖 可用命令:');
        console.log('  help          - 显示此帮助信息');
        console.log('  clear         - 清屏');
        console.log('  exit/quit     - 退出程序');
        console.log('  send <cmd>    - 发送PowerShell命令');
        console.log('  <任何命令>     - 直接发送到PowerShell');
        console.log('\n💡 示例:');
        console.log('  Get-Date');
        console.log('  chcp 65001');
        console.log('  $env:PYTHONIOENCODING = "utf-8"');
        console.log('  acli rovodev run');
        console.log('');
    }

    close() {
        console.log('🔄 正在关闭PowerShell进程...');
        
        if (this.rl) {
            this.rl.close();
        }
        
        if (this.psProcess && this.psProcess.stdin && !this.psProcess.stdin.destroyed) {
            this.psProcess.stdin.write('exit\n');
            this.psProcess.stdin.end();
        }
        
        setTimeout(() => {
            if (this.psProcess && !this.psProcess.killed) {
                this.psProcess.kill('SIGTERM');
            }
        }, 1000);
    }

    // 自动化命令序列
    async runCommandSequence(commands) {
        console.log('🔄 执行命令序列...');
        
        for (const command of commands) {
            console.log(`⏳ 执行: ${command}`);
            this.sendCommand(command);
            
            // 等待一段时间让命令执行
            await new Promise(resolve => setTimeout(resolve, 1000));
        }
        
        console.log('✅ 命令序列执行完成');
    }
}

// 使用示例
async function main() {
    const ps = new PowerShellInteractive();
    
    try {
        await ps.start();
        
        // 可以选择性地运行一些初始化命令
        const initCommands = [
            'chcp 65001',
            '$OutputEncoding = [System.Text.Encoding]::UTF8',
            '[Console]::OutputEncoding = [System.Text.Encoding]::UTF8',
            '$env:PYTHONIOENCODING = "utf-8"',
            '$env:PYTHONLEGACYWINDOWSSTDIO = "1"'
        ];
        
        console.log('🔧 是否要运行初始化命令? (y/n)');
        // 这里可以添加用户确认逻辑
        
        // 开始交互模式
        ps.rl.prompt();
        
    } catch (error) {
        console.error('❌ 启动失败:', error.message);
        process.exit(1);
    }
}

// 处理程序退出
process.on('SIGINT', () => {
    console.log('\n👋 接收到退出信号...');
    process.exit(0);
});

if (require.main === module) {
    main();
}

module.exports = PowerShellInteractive;
