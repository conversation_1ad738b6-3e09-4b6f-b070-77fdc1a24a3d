// encoding-fix.js - 专门解决ACLI编码问题的脚本

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

class EncodingFixer {
    constructor() {
        this.workingDir = path.resolve('./test1');
        this.psProcess = null;
    }

    async fixAndRunACLI() {
        console.log('🔧 ACLI编码问题修复器');
        console.log('=' * 40);
        
        // 确保工作目录存在
        this.ensureWorkingDirectory();
        
        // 方法1: 尝试通过环境变量修复
        console.log('\n📋 方法1: 通过PowerShell环境变量修复');
        await this.tryMethod1();
        
        // 方法2: 创建包装脚本
        console.log('\n📋 方法2: 创建Python包装脚本');
        await this.tryMethod2();
        
        // 方法3: 使用CMD而不是PowerShell
        console.log('\n📋 方法3: 使用CMD启动');
        await this.tryMethod3();
    }

    ensureWorkingDirectory() {
        if (!fs.existsSync(this.workingDir)) {
            fs.mkdirSync(this.workingDir, { recursive: true });
            console.log(`📁 创建工作目录: ${this.workingDir}`);
        }
    }

    async tryMethod1() {
        console.log('🚀 尝试PowerShell环境变量方法...');
        
        const psCommand = `
            # 设置控制台编码
            chcp 65001 | Out-Null
            
            # 设置PowerShell编码
            $OutputEncoding = [System.Text.Encoding]::UTF8
            [Console]::OutputEncoding = [System.Text.Encoding]::UTF8
            [Console]::InputEncoding = [System.Text.Encoding]::UTF8
            
            # 设置Python环境变量
            $env:PYTHONIOENCODING = "utf-8"
            $env:PYTHONLEGACYWINDOWSSTDIO = "1"
            $env:PYTHONUTF8 = "1"
            $env:PYTHONUNBUFFERED = "1"
            
            # 设置系统编码环境变量
            $env:LANG = "en_US.UTF-8"
            $env:LC_ALL = "en_US.UTF-8"
            $env:LC_CTYPE = "en_US.UTF-8"
            
            # 设置Rich库环境变量
            $env:FORCE_COLOR = "0"
            $env:NO_COLOR = "1"
            $env:TERM = "dumb"
            
            # 切换到工作目录
            Set-Location "${this.workingDir}"
            
            # 启动ACLI
            Write-Host "🤖 启动ACLI Rovodev..."
            acli rovodev run
        `;

        return this.runPowerShellCommand(psCommand);
    }

    async tryMethod2() {
        console.log('🐍 创建Python包装脚本...');
        
        const pythonWrapper = `#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import sys
import os
import locale
import subprocess

# 强制设置编码
os.environ['PYTHONIOENCODING'] = 'utf-8'
os.environ['PYTHONUTF8'] = '1'
os.environ['PYTHONLEGACYWINDOWSSTDIO'] = '1'
os.environ['PYTHONUNBUFFERED'] = '1'

# 设置locale
try:
    locale.setlocale(locale.LC_ALL, 'en_US.UTF-8')
except:
    try:
        locale.setlocale(locale.LC_ALL, 'C.UTF-8')
    except:
        pass

# 设置Rich库环境变量
os.environ['FORCE_COLOR'] = '0'
os.environ['NO_COLOR'] = '1'
os.environ['TERM'] = 'dumb'

# 重新设置stdout和stderr编码
if hasattr(sys.stdout, 'reconfigure'):
    sys.stdout.reconfigure(encoding='utf-8', errors='replace')
if hasattr(sys.stderr, 'reconfigure'):
    sys.stderr.reconfigure(encoding='utf-8', errors='replace')

print("🤖 启动ACLI Rovodev (通过Python包装器)...")

# 启动ACLI
try:
    result = subprocess.run(['acli', 'rovodev', 'run'], 
                          cwd=r'${this.workingDir}',
                          encoding='utf-8',
                          errors='replace',
                          text=True)
    sys.exit(result.returncode)
except Exception as e:
    print(f"❌ 启动失败: {e}")
    sys.exit(1)
`;

        const wrapperPath = path.join(this.workingDir, 'acli_wrapper.py');
        fs.writeFileSync(wrapperPath, pythonWrapper, 'utf8');
        console.log(`📝 创建包装脚本: ${wrapperPath}`);
        
        // 使用Python运行包装脚本
        const psCommand = `
            chcp 65001 | Out-Null
            Set-Location "${this.workingDir}"
            python acli_wrapper.py
        `;
        
        return this.runPowerShellCommand(psCommand);
    }

    async tryMethod3() {
        console.log('💻 尝试CMD方法...');
        
        const batScript = `@echo off
chcp 65001 >nul
set PYTHONIOENCODING=utf-8
set PYTHONUTF8=1
set PYTHONLEGACYWINDOWSSTDIO=1
set PYTHONUNBUFFERED=1
set LANG=en_US.UTF-8
set LC_ALL=en_US.UTF-8
set LC_CTYPE=en_US.UTF-8
set FORCE_COLOR=0
set NO_COLOR=1
set TERM=dumb
cd /d "${this.workingDir}"
echo 🤖 启动ACLI Rovodev (通过CMD)...
acli rovodev run
pause`;

        const batPath = path.join(this.workingDir, 'run_acli.bat');
        fs.writeFileSync(batPath, batScript, 'utf8');
        console.log(`📝 创建批处理脚本: ${batPath}`);
        
        // 使用CMD运行
        return this.runCMDCommand(batPath);
    }

    async runPowerShellCommand(psCommand) {
        return new Promise((resolve) => {
            console.log('🚀 启动PowerShell进程...');
            
            this.psProcess = spawn('powershell', [
                '-ExecutionPolicy', 'Bypass',
                '-Command', psCommand
            ], {
                stdio: ['pipe', 'pipe', 'pipe'],
                cwd: this.workingDir
            });

            this.setupEventHandlers(resolve);
        });
    }

    async runCMDCommand(batPath) {
        return new Promise((resolve) => {
            console.log('🚀 启动CMD进程...');
            
            this.psProcess = spawn('cmd', ['/c', batPath], {
                stdio: ['pipe', 'pipe', 'pipe'],
                cwd: this.workingDir
            });

            this.setupEventHandlers(resolve);
        });
    }

    setupEventHandlers(resolve) {
        let hasOutput = false;
        
        // 监听标准输出
        this.psProcess.stdout.on('data', (data) => {
            const output = data.toString('utf8');
            console.log('📤 输出:', output.trim());
            hasOutput = true;
            
            // 检测成功启动
            if (output.includes('Welcome to Rovo Dev') || 
                output.includes('Atlassian')) {
                console.log('✅ ACLI成功启动！');
            }
        });

        // 监听标准错误
        this.psProcess.stderr.on('data', (data) => {
            const error = data.toString('utf8');
            
            // 过滤掉编码错误
            if (!error.includes('UnicodeEncodeError') && 
                !error.includes('gbk') && 
                error.trim()) {
                console.error('❌ 错误:', error.trim());
            } else if (error.includes('UnicodeEncodeError')) {
                console.log('⚠️ 检测到编码错误，但继续运行...');
            }
        });

        // 监听进程关闭
        this.psProcess.on('close', (code) => {
            console.log(`\n📘 进程退出，退出码: ${code}`);
            if (code === 0 || hasOutput) {
                console.log('✅ 进程正常结束');
            } else {
                console.log('❌ 进程异常结束');
            }
            resolve(code);
        });

        this.psProcess.on('error', (error) => {
            console.error('❌ 进程启动错误:', error.message);
            resolve(-1);
        });

        // 处理Ctrl+C
        process.on('SIGINT', () => {
            console.log('\n👋 接收到退出信号...');
            if (this.psProcess && !this.psProcess.killed) {
                this.psProcess.kill('SIGTERM');
            }
            process.exit(0);
        });
    }

    // 发送命令到进程
    sendCommand(command) {
        if (this.psProcess && this.psProcess.stdin && !this.psProcess.stdin.destroyed) {
            console.log(`📥 发送命令: ${command}`);
            this.psProcess.stdin.write(command + '\n');
        } else {
            console.error('❌ 无法发送命令，进程不可用');
        }
    }
}

// 主函数
async function main() {
    const fixer = new EncodingFixer();
    
    console.log('🎯 选择修复方法:');
    console.log('1. PowerShell环境变量方法 (推荐)');
    console.log('2. Python包装脚本方法');
    console.log('3. CMD批处理方法');
    console.log('4. 全部尝试');
    
    const args = process.argv.slice(2);
    const method = args[0] || '1';
    
    try {
        switch (method) {
            case '1':
                await fixer.tryMethod1();
                break;
            case '2':
                await fixer.tryMethod2();
                break;
            case '3':
                await fixer.tryMethod3();
                break;
            case '4':
            default:
                await fixer.fixAndRunACLI();
                break;
        }
    } catch (error) {
        console.error('❌ 修复失败:', error.message);
        process.exit(1);
    }
}

if (require.main === module) {
    main();
}

module.exports = EncodingFixer;
