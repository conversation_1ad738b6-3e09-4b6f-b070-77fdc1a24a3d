/* style.css - ACLI聊天界面样式 */

:root {
    --primary-color: #0066cc;
    --secondary-color: #f5f5f5;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --dark-color: #343a40;
    --light-color: #ffffff;
    --border-color: #dee2e6;
    --text-color: #333333;
    --text-muted: #6c757d;
    --shadow: 0 2px 10px rgba(0,0,0,0.1);
    --border-radius: 8px;
    --transition: all 0.3s ease;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    height: 100vh;
    overflow: hidden;
}

.chat-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    max-width: 1200px;
    margin: 0 auto;
    background: var(--light-color);
    box-shadow: var(--shadow);
}

/* 头部样式 */
.chat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
    background: var(--light-color);
    border-bottom: 1px solid var(--border-color);
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.header-left {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.header-left i {
    font-size: 1.5rem;
    color: var(--primary-color);
}

.header-left h1 {
    font-size: 1.25rem;
    color: var(--text-color);
    margin: 0;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    background: var(--secondary-color);
    font-size: 0.875rem;
}

.status-indicator.connected {
    background: rgba(40, 167, 69, 0.1);
    color: var(--success-color);
}

.status-indicator.disconnected {
    background: rgba(220, 53, 69, 0.1);
    color: var(--danger-color);
}

.status-indicator.connecting {
    background: rgba(255, 193, 7, 0.1);
    color: var(--warning-color);
}

.header-right {
    display: flex;
    gap: 0.5rem;
}

.btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-secondary {
    background: var(--secondary-color);
    color: var(--text-color);
}

.btn-secondary:hover {
    background: var(--border-color);
}

/* 聊天消息区域 */
.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 1rem;
    background: #fafafa;
}

.welcome-message {
    text-align: center;
    padding: 2rem;
    color: var(--text-muted);
}

.welcome-message .message-content {
    max-width: 400px;
    margin: 0 auto;
}

.welcome-message i {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.welcome-message h3 {
    margin-bottom: 0.5rem;
    color: var(--text-color);
}

.loading-dots {
    display: flex;
    justify-content: center;
    gap: 0.25rem;
    margin-top: 1rem;
}

.loading-dots span {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--primary-color);
    animation: loading 1.4s infinite ease-in-out;
}

.loading-dots span:nth-child(1) { animation-delay: -0.32s; }
.loading-dots span:nth-child(2) { animation-delay: -0.16s; }

@keyframes loading {
    0%, 80%, 100% { transform: scale(0); }
    40% { transform: scale(1); }
}

.message {
    margin-bottom: 1rem;
    display: flex;
    flex-direction: column;
}

.message.user {
    align-items: flex-end;
}

.message.assistant {
    align-items: flex-start;
}

.message.system {
    align-items: center;
}

.message-bubble {
    max-width: 70%;
    padding: 0.75rem 1rem;
    border-radius: var(--border-radius);
    word-wrap: break-word;
    position: relative;
}

.message.user .message-bubble {
    background: var(--primary-color);
    color: white;
    border-bottom-right-radius: 4px;
}

.message.assistant .message-bubble {
    background: var(--light-color);
    color: var(--text-color);
    border: 1px solid var(--border-color);
    border-bottom-left-radius: 4px;
}

.message.system .message-bubble {
    background: var(--secondary-color);
    color: var(--text-muted);
    font-size: 0.875rem;
    max-width: 90%;
}

.message-timestamp {
    font-size: 0.75rem;
    color: var(--text-muted);
    margin-top: 0.25rem;
}

.message.user .message-timestamp {
    text-align: right;
}

.message.assistant .message-timestamp {
    text-align: left;
}

.message.system .message-timestamp {
    text-align: center;
}

/* 输入区域 */
.chat-input-container {
    padding: 1rem 1.5rem;
    background: var(--light-color);
    border-top: 1px solid var(--border-color);
}

.input-wrapper {
    display: flex;
    gap: 0.75rem;
    align-items: flex-end;
}

#messageInput {
    flex: 1;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    resize: none;
    font-family: inherit;
    font-size: 0.875rem;
    line-height: 1.4;
    max-height: 120px;
    transition: var(--transition);
}

#messageInput:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(0, 102, 204, 0.1);
}

#messageInput:disabled {
    background: var(--secondary-color);
    cursor: not-allowed;
}

.send-btn {
    padding: 0.75rem;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    min-width: 44px;
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.send-btn:hover:not(:disabled) {
    background: #0056b3;
}

.send-btn:disabled {
    background: var(--text-muted);
    cursor: not-allowed;
}

.input-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 0.5rem;
    font-size: 0.75rem;
    color: var(--text-muted);
}

/* 设置面板 */
.settings-panel {
    position: fixed;
    top: 0;
    right: -400px;
    width: 400px;
    height: 100vh;
    background: var(--light-color);
    box-shadow: -2px 0 10px rgba(0,0,0,0.1);
    transition: var(--transition);
    z-index: 1000;
}

.settings-panel.open {
    right: 0;
}

.settings-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid var(--border-color);
}

.settings-header h3 {
    margin: 0;
    color: var(--text-color);
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.25rem;
    cursor: pointer;
    color: var(--text-muted);
    padding: 0.25rem;
}

.close-btn:hover {
    color: var(--text-color);
}

.settings-body {
    padding: 1.5rem;
}

.setting-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
    border-bottom: 1px solid var(--border-color);
}

.setting-item:last-child {
    border-bottom: none;
}

.setting-item label {
    font-weight: 500;
    color: var(--text-color);
}

.setting-item input[type="checkbox"] {
    width: 20px;
    height: 20px;
    cursor: pointer;
}

.setting-item select {
    padding: 0.25rem 0.5rem;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background: var(--light-color);
    cursor: pointer;
}

/* 连接状态提示 */
.connection-toast {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%) translateY(-100px);
    background: var(--danger-color);
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    box-shadow: var(--shadow);
    transition: var(--transition);
    z-index: 1001;
}

.connection-toast.show {
    transform: translateX(-50%) translateY(0);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .chat-container {
        height: 100vh;
        border-radius: 0;
    }
    
    .chat-header {
        padding: 0.75rem 1rem;
    }
    
    .header-left h1 {
        font-size: 1.1rem;
    }
    
    .message-bubble {
        max-width: 85%;
    }
    
    .settings-panel {
        width: 100%;
        right: -100%;
    }
    
    .chat-input-container {
        padding: 1rem;
    }
}

/* 深色主题 */
@media (prefers-color-scheme: dark) {
    :root {
        --light-color: #2d3748;
        --secondary-color: #4a5568;
        --text-color: #e2e8f0;
        --text-muted: #a0aec0;
        --border-color: #4a5568;
    }
    
    body {
        background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
    }
    
    .chat-messages {
        background: #1a202c;
    }
    
    .message.assistant .message-bubble {
        background: #2d3748;
        border-color: #4a5568;
    }
    
    #messageInput {
        background: #2d3748;
        color: var(--text-color);
        border-color: #4a5568;
    }
    
    #messageInput:disabled {
        background: #4a5568;
    }
}
