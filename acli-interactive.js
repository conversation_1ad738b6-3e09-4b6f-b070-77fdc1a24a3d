// acli-interactive.js - 专门用于ACLI Rovodev的交互式控制器

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

class ACLIController {
    constructor() {
        this.psProcess = null;
        this.workingDir = path.resolve('./test1');
        this.isAcliRunning = false;
        this.outputBuffer = '';
    }

    async start() {
        console.log('🚀 启动ACLI Rovodev控制器...');
        
        // 确保工作目录存在
        this.ensureWorkingDirectory();
        
        // 启动PowerShell进程
        await this.startPowerShell();
        
        // 设置编码和环境
        await this.setupEnvironment();
        
        // 启动ACLI
        await this.startACLI();
        
        console.log('✅ ACLI控制器已准备就绪');
    }

    ensureWorkingDirectory() {
        if (!fs.existsSync(this.workingDir)) {
            fs.mkdirSync(this.workingDir, { recursive: true });
            console.log(`📁 创建工作目录: ${this.workingDir}`);
        }
    }

    async startPowerShell() {
        return new Promise((resolve, reject) => {
            console.log('🔧 启动PowerShell进程...');
            
            this.psProcess = spawn('powershell', [
                '-ExecutionPolicy', 'Bypass',
                '-NoExit',
                '-Command', '-'
            ], {
                stdio: ['pipe', 'pipe', 'pipe'],
                cwd: this.workingDir
            });

            this.setupEventHandlers();
            
            // 等待PowerShell准备就绪
            setTimeout(() => {
                console.log('✅ PowerShell已启动');
                resolve();
            }, 1000);
        });
    }

    setupEventHandlers() {
        // 监听标准输出
        this.psProcess.stdout.on('data', (data) => {
            const output = data.toString('utf8');
            this.outputBuffer += output;
            
            console.log('📤 输出:', output.trim());
            
            // 检测ACLI状态
            this.detectACLIStatus(output);
            
            // 处理特定输出
            this.handleACLIOutput(output);
        });

        // 监听标准错误
        this.psProcess.stderr.on('data', (data) => {
            const error = data.toString('utf8');
            console.error('❌ 错误:', error.trim());
        });

        // 监听进程关闭
        this.psProcess.on('close', (code) => {
            console.log(`\n📘 进程退出，退出码: ${code}`);
            this.isAcliRunning = false;
        });

        this.psProcess.on('error', (error) => {
            console.error('❌ 进程错误:', error.message);
        });
    }

    async setupEnvironment() {
        console.log('🔧 设置环境变量和编码...');
        
        const commands = [
            'chcp 65001',
            '$OutputEncoding = [System.Text.Encoding]::UTF8',
            '[Console]::OutputEncoding = [System.Text.Encoding]::UTF8',
            '$env:PYTHONIOENCODING = "utf-8"',
            '$env:PYTHONLEGACYWINDOWSSTDIO = "1"',
            '$env:LANG = "C.UTF-8"',
            '$env:LC_ALL = "C.UTF-8"'
        ];

        for (const command of commands) {
            await this.sendCommand(command);
            await this.wait(500); // 等待命令执行
        }
        
        console.log('✅ 环境设置完成');
    }

    async startACLI() {
        console.log('🤖 启动ACLI Rovodev...');
        await this.sendCommand('acli rovodev run');
        this.isAcliRunning = true;
    }

    async sendCommand(command) {
        return new Promise((resolve) => {
            if (this.psProcess && this.psProcess.stdin && !this.psProcess.stdin.destroyed) {
                console.log(`📥 发送命令: ${command}`);
                this.psProcess.stdin.write(command + '\n');
                resolve();
            } else {
                console.error('❌ 无法发送命令，进程不可用');
                resolve();
            }
        });
    }

    detectACLIStatus(output) {
        if (output.includes('rovodev') && output.includes('run')) {
            console.log('🎯 检测到ACLI Rovodev正在运行');
        }
        
        if (output.includes('Listening on') || output.includes('Server running')) {
            console.log('🌐 检测到服务器已启动');
        }
        
        if (output.includes('Error') || output.includes('Exception')) {
            console.log('⚠️ 检测到错误，可能需要重启');
            this.isAcliRunning = false;
        }
    }

    handleACLIOutput(output) {
        // 根据输出内容自动响应
        if (output.includes('请输入') || output.includes('Enter')) {
            console.log('💬 ACLI正在等待输入');
        }
        
        if (output.includes('选择') || output.includes('choice')) {
            console.log('🔢 ACLI正在等待选择');
        }
        
        // 可以在这里添加自动化响应逻辑
        if (output.includes('是否继续')) {
            console.log('🤔 自动响应：是');
            this.sendCommand('y');
        }
    }

    // 发送用户输入到ACLI
    async sendUserInput(input) {
        if (this.isAcliRunning) {
            console.log(`👤 用户输入: ${input}`);
            await this.sendCommand(input);
        } else {
            console.log('❌ ACLI未运行，无法发送输入');
        }
    }

    // 重启ACLI
    async restartACLI() {
        console.log('🔄 重启ACLI...');
        
        // 停止当前ACLI
        await this.sendCommand('exit');
        await this.wait(2000);
        
        // 重新启动
        await this.startACLI();
    }

    // 获取最近的输出
    getRecentOutput(lines = 10) {
        const outputLines = this.outputBuffer.split('\n');
        return outputLines.slice(-lines).join('\n');
    }

    // 等待指定时间
    wait(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // 关闭控制器
    async close() {
        console.log('🔄 正在关闭ACLI控制器...');
        
        if (this.isAcliRunning) {
            await this.sendCommand('exit');
            await this.wait(1000);
        }
        
        if (this.psProcess && this.psProcess.stdin && !this.psProcess.stdin.destroyed) {
            this.psProcess.stdin.write('exit\n');
            this.psProcess.stdin.end();
        }
        
        setTimeout(() => {
            if (this.psProcess && !this.psProcess.killed) {
                this.psProcess.kill('SIGTERM');
            }
        }, 2000);
    }

    // 检查ACLI状态
    getStatus() {
        return {
            isRunning: this.isAcliRunning,
            workingDir: this.workingDir,
            processAlive: this.psProcess && !this.psProcess.killed
        };
    }
}

// 使用示例
async function main() {
    const acli = new ACLIController();
    
    try {
        await acli.start();
        
        // 示例：延迟发送一些命令
        setTimeout(async () => {
            console.log('\n🎮 开始交互示例...');
            
            // 可以发送各种命令到ACLI
            // await acli.sendUserInput('help');
            // await acli.sendUserInput('1'); // 选择选项1
            
        }, 5000);
        
        // 设置定期状态检查
        setInterval(() => {
            const status = acli.getStatus();
            console.log('📊 状态:', status);
        }, 30000);
        
    } catch (error) {
        console.error('❌ 启动失败:', error.message);
        process.exit(1);
    }
    
    // 处理程序退出
    process.on('SIGINT', async () => {
        console.log('\n👋 接收到退出信号...');
        await acli.close();
        process.exit(0);
    });
}

if (require.main === module) {
    main();
}

module.exports = ACLIController;
