// json-api-wrapper.js - JSON API 包装器，提供结构化的交互接口

const { AcliInteractive } = require('./acli-rovodev-interactive-enhanced');
const EventEmitter = require('events');

class AcliJsonAPI extends EventEmitter {
    constructor(options = {}) {
        super();

        this.acli = new AcliInteractive({
            ...options,
            outputMode: 'json'
        });

        this.conversations = new Map();
        this.isReady = false;

        this.setupEventHandlers();
    }

    setupEventHandlers() {
        this.acli.on('processStarted', () => {
            this.isReady = true;
            this.emit('ready');
        });

        this.acli.on('responseComplete', (data) => {
            const conversation = this.conversations.get(data.conversationId);
            if (conversation) {
                conversation.status = 'completed';
                conversation.response = this.parseResponse(data.response);
                conversation.endTime = new Date().toISOString();

                this.emit('conversationComplete', conversation);
            }
        });

        this.acli.on('commandSent', (data) => {
            const conversation = {
                id: data.conversationId,
                command: data.command,
                sessionId: data.sessionId,
                status: 'processing',
                startTime: new Date().toISOString(),
                response: null
            };

            this.conversations.set(data.conversationId, conversation);
            this.emit('conversationStarted', conversation);
        });
    }

    // 启动API
    async start() {
        await this.acli.start();
        return this;
    }

    // 发送命令并返回Promise
    async sendCommand(command, options = {}) {
        return new Promise((resolve, reject) => {
            if (!this.isReady) {
                reject(new Error('ACLI not ready'));
                return;
            }

            const timeout = options.timeout || 30000;
            const timeoutId = setTimeout(() => {
                reject(new Error('Command timeout'));
            }, timeout);

            // 监听这次对话完成
            const onComplete = (conversation) => {
                if (conversation.command === command) {
                    clearTimeout(timeoutId);
                    this.off('conversationComplete', onComplete);
                    resolve(conversation);
                }
            };

            this.on('conversationComplete', onComplete);

            // 发送命令
            this.acli.sendPersistentCommand(command);
        });
    }

    // 批量发送命令
    async sendCommands(commands, options = {}) {
        const results = [];
        const concurrent = options.concurrent || false;

        if (concurrent) {
            // 并发执行
            const promises = commands.map(cmd => this.sendCommand(cmd, options));
            results.push(...await Promise.allSettled(promises));
        } else {
            // 顺序执行
            for (const command of commands) {
                try {
                    const result = await this.sendCommand(command, options);
                    results.push({ status: 'fulfilled', value: result });
                } catch (error) {
                    results.push({ status: 'rejected', reason: error });
                }
            }
        }

        return results;
    }

    // 解析响应数据
    parseResponse(rawResponse) {
        const parsed = {
            raw: rawResponse,
            content: '',
            metadata: {},
            tools: [],
            files: [],
            errors: []
        };

        const lines = rawResponse.split('\n');
        let currentSection = 'content';
        let contentLines = [];

        for (const line of lines) {
            // 提取内容
            if (line.includes('╭─ Response')) {
                currentSection = 'response';
                continue;
            }

            if (line.includes('╰─')) {
                currentSection = 'content';
                continue;
            }

            // 提取工具调用
            const toolMatch = line.match(/└── Calling (\w+):/);
            if (toolMatch) {
                parsed.tools.push({
                    name: toolMatch[1],
                    timestamp: new Date().toISOString()
                });
                continue;
            }

            // 提取文件创建
            const fileMatch = line.match(/Successfully created (.+)\./);
            if (fileMatch) {
                parsed.files.push({
                    name: fileMatch[1],
                    timestamp: new Date().toISOString()
                });
                continue;
            }

            // 提取会话信息
            const sessionMatch = line.match(/Session context:\s*▮*\s*([\d.]+K?)\/(\d+K?)/);
            if (sessionMatch) {
                parsed.metadata.sessionContext = {
                    used: sessionMatch[1],
                    total: sessionMatch[2]
                };
                continue;
            }

            // 提取每日统计
            const dailyMatch = line.match(/Daily total:\s*▮*\s*([\d.]+[KM]?)\/(\d+[KM]?)/);
            if (dailyMatch) {
                parsed.metadata.dailyTotal = {
                    used: dailyMatch[1],
                    total: dailyMatch[2]
                };
                continue;
            }

            // 提取错误
            if (line.includes('Error') || line.includes('exception')) {
                parsed.errors.push({
                    message: line.trim(),
                    timestamp: new Date().toISOString()
                });
                continue;
            }

            // 收集内容
            if (currentSection === 'response' && line.trim() !== '') {
                contentLines.push(line.replace(/^│\s?/, '').replace(/\s*│$/, ''));
            }
        }

        parsed.content = contentLines.join('\n').trim();
        return parsed;
    }

    // 获取对话历史
    getConversations(limit = 10) {
        const conversations = Array.from(this.conversations.values())
            .sort((a, b) => new Date(b.startTime) - new Date(a.startTime))
            .slice(0, limit);

        return conversations;
    }

    // 获取统计信息
    getStats() {
        const conversations = Array.from(this.conversations.values());
        const completed = conversations.filter(c => c.status === 'completed');
        const processing = conversations.filter(c => c.status === 'processing');

        return {
            total: conversations.length,
            completed: completed.length,
            processing: processing.length,
            averageResponseTime: this.calculateAverageResponseTime(completed),
            sessionId: this.acli.sessionId,
            isReady: this.isReady
        };
    }

    calculateAverageResponseTime(conversations) {
        if (conversations.length === 0) return 0;

        const totalTime = conversations.reduce((sum, conv) => {
            if (conv.startTime && conv.endTime) {
                return sum + (new Date(conv.endTime) - new Date(conv.startTime));
            }
            return sum;
        }, 0);

        return Math.round(totalTime / conversations.length);
    }

    // 清理
    async cleanup() {
        await this.acli.cleanup();
    }
}

// 流式JSON输出处理器
class AcliStreamAPI extends EventEmitter {
    constructor(options = {}) {
        super();

        this.acli = new AcliInteractive({
            ...options,
            outputMode: 'stream'
        });

        this.currentStream = null;
        this.setupEventHandlers();
    }

    setupEventHandlers() {
        this.acli.on('responseComplete', (data) => {
            if (this.currentStream) {
                this.currentStream.status = 'completed';
                this.emit('streamComplete', this.currentStream);
                this.currentStream = null;
            }
        });

        this.acli.on('commandSent', (data) => {
            this.currentStream = {
                id: data.conversationId,
                command: data.command,
                chunks: [],
                status: 'streaming',
                startTime: new Date().toISOString()
            };

            this.emit('streamStarted', this.currentStream);
        });
    }

    async start() {
        // 重写输出处理来支持流式
        const originalHandleOutput = this.acli.handleOutput;
        this.acli.handleOutput = (data) => {
            if (this.currentStream) {
                const chunk = {
                    id: `chunk_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
                    data: data.toString('utf8'),
                    timestamp: new Date().toISOString(),
                    streamId: this.currentStream.id
                };

                this.currentStream.chunks.push(chunk);
                this.emit('streamChunk', chunk);
            }

            // 调用原始处理方法
            originalHandleOutput.call(this.acli, data);
        };

        await this.acli.start();
        return this;
    }

    async sendStreamCommand(command) {
        return new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
                reject(new Error('Stream timeout'));
            }, 60000);

            const onComplete = (stream) => {
                if (stream.command === command) {
                    clearTimeout(timeout);
                    this.off('streamComplete', onComplete);
                    resolve(stream);
                }
            };

            this.on('streamComplete', onComplete);
            this.acli.sendPersistentCommand(command);
        });
    }
}

// 使用示例
async function jsonApiExample() {
    const api = new AcliJsonAPI({
        workingDirectory: 'D:\\test-list\\rovo-run-list\\test1'
    });

    // 监听事件
    api.on('ready', () => {
        console.log('JSON API 准备就绪');
    });

    api.on('conversationComplete', (conversation) => {
        console.log('对话完成:', JSON.stringify(conversation, null, 2));
    });

    await api.start();

    // 发送单个命令
    try {
        const result = await api.sendCommand('你好，请介绍一下你自己');
        console.log('命令结果:', result);
    } catch (error) {
        console.error('命令失败:', error);
    }

    // 批量发送命令
    const commands = [
        '帮我创建一个Python计算器',
        '为这个计算器添加单元测试',
        '解释一下这个代码的工作原理'
    ];

    const results = await api.sendCommands(commands);
    console.log('批量结果:', results);

    // 获取统计信息
    console.log('API统计:', api.getStats());
}

// 流式API示例
async function streamApiExample() {
    const streamApi = new AcliStreamAPI({
        workingDirectory: 'D:\\test-list\\rovo-run-list\\test1'
    });

    streamApi.on('streamChunk', (chunk) => {
        console.log('流式数据块:', chunk);
    });

    streamApi.on('streamComplete', (stream) => {
        console.log('流式完成:', stream.chunks.length, '个数据块');
    });

    await streamApi.start();

    const stream = await streamApi.sendStreamCommand('创建一个复杂的React组件');
    console.log('流式响应完成:', stream);
}

module.exports = {
    AcliJsonAPI,
    AcliStreamAPI,
    jsonApiExample,
    streamApiExample
};