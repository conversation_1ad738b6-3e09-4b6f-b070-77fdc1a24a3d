// verify-enhanced.js - 验证增强版脚本是否正常

console.log('🔍 验证 acli-rovodev-interactive-enhanced.js');
console.log('=' * 50);

try {
    // 1. 检查文件是否可以正常加载
    console.log('📋 步骤1: 检查文件加载...');
    const { AcliInteractive } = require('./acli-rovodev-interactive-enhanced');
    console.log('✅ 文件加载成功');

    // 2. 检查类是否可以实例化
    console.log('\n📋 步骤2: 检查类实例化...');
    const interactive = new AcliInteractive({
        workingDirectory: './test1',
        outputMode: 'console',
        responseTimeout: 5000
    });
    console.log('✅ 类实例化成功');

    // 3. 检查主要方法是否存在
    console.log('\n📋 步骤3: 检查方法存在性...');
    const methods = ['start', 'cleanup', 'log', 'handleOutput'];
    methods.forEach(method => {
        if (typeof interactive[method] === 'function') {
            console.log(`✅ 方法 ${method} 存在`);
        } else {
            console.log(`❌ 方法 ${method} 不存在`);
        }
    });

    // 4. 检查事件发射器功能
    console.log('\n📋 步骤4: 检查事件发射器...');
    if (typeof interactive.on === 'function' && typeof interactive.emit === 'function') {
        console.log('✅ 事件发射器功能正常');
    } else {
        console.log('❌ 事件发射器功能异常');
    }

    // 5. 测试事件监听
    console.log('\n📋 步骤5: 测试事件监听...');
    interactive.on('test', () => {
        console.log('✅ 事件监听测试成功');
    });
    interactive.emit('test');

    console.log('\n🎯 验证结果: 文件完全正常！');
    console.log('💡 可以使用以下方式运行:');
    console.log('   - 直接运行: node acli-rovodev-interactive-enhanced.js');
    console.log('   - 模块导入: require("./acli-rovodev-interactive-enhanced")');
    console.log('   - npm脚本: npm run test:enhanced');

} catch (error) {
    console.error('❌ 验证失败:', error.message);
    console.log('\n🔍 错误详情:');
    console.log(error.stack);
    
    console.log('\n💡 可能的解决方案:');
    console.log('1. 检查文件语法: node -c acli-rovodev-interactive-enhanced.js');
    console.log('2. 检查依赖: npm install');
    console.log('3. 检查文件权限');
    
    process.exit(1);
}
