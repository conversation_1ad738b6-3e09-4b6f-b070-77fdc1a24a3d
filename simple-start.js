// simple-start.js - 简化的启动脚本，专门解决编码问题

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🔧 ACLI Rovodev 简化启动器');
console.log('=' * 40);

// 确保工作目录存在
const workingDir = path.resolve('./test1');
if (!fs.existsSync(workingDir)) {
    fs.mkdirSync(workingDir, { recursive: true });
    console.log(`📁 创建工作目录: ${workingDir}`);
}

console.log(`📁 工作目录: ${workingDir}`);

// 设置环境变量以解决编码问题
const env = {
    ...process.env,
    // 强制使用UTF-8编码
    PYTHONIOENCODING: 'utf-8',
    PYTHONLEGACYWINDOWSSTDIO: '1',
    
    // 使用英文环境避免中文编码问题
    LANG: 'C.UTF-8',
    LC_ALL: 'C.UTF-8',
    
    // Windows代码页设置
    CHCP: '65001'
};

console.log('🌍 环境变量设置:');
console.log(`  PYTHONIOENCODING: ${env.PYTHONIOENCODING}`);
console.log(`  PYTHONLEGACYWINDOWSSTDIO: ${env.PYTHONLEGACYWINDOWSSTDIO}`);
console.log(`  LANG: ${env.LANG}`);
console.log(`  LC_ALL: ${env.LC_ALL}`);

console.log('\n🚀 启动 acli rovodev run...');
console.log('💡 提示: 如果仍有编码问题，请尝试在PowerShell中运行');

// 启动acli进程
const acliProcess = spawn('acli', ['rovodev', 'run'], {
    cwd: workingDir,
    stdio: 'inherit',
    env: env,
    shell: true
});

// 错误处理
acliProcess.on('error', (error) => {
    console.error('\n❌ 启动失败:', error.message);
    console.log('\n🔍 可能的解决方案:');
    console.log('1. 确保已安装 Atlassian CLI');
    console.log('2. 检查 acli 命令是否在 PATH 中');
    console.log('3. 尝试在 PowerShell 中运行');
    console.log('4. 运行: npm run acli');
    process.exit(1);
});

// 进程退出处理
acliProcess.on('close', (code) => {
    console.log(`\n📘 acli 进程退出，退出码: ${code}`);
    if (code !== 0) {
        console.log('\n🔍 如果遇到编码错误，请尝试:');
        console.log('1. 在 PowerShell 中运行: chcp 65001');
        console.log('2. 然后运行: npm run acli:direct');
        console.log('3. 或者直接运行: start-acli.bat');
    }
});

// 处理Ctrl+C
process.on('SIGINT', () => {
    console.log('\n👋 接收到退出信号...');
    acliProcess.kill('SIGINT');
    process.exit(0);
});

// 处理未捕获的异常
process.on('uncaughtException', (error) => {
    console.error('\n❌ 未捕获的异常:', error.message);
    process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
    console.error('\n❌ 未处理的Promise拒绝:', reason);
    process.exit(1);
});
