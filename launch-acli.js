// launch-acli.js - 简化的启动脚本

const { AcliInteractive } = require('./acli-rovodev-interactive-enhanced');
const { AcliJsonAPI, AcliStreamAPI } = require('./json-api-wrapper');

// 配置文件
const CONFIG = {
    // 基本配置
    workingDirectory: process.env.ACLI_WORKING_DIR || 'D:\\test-list\\rovo-run-list\\test1',
    responseTimeout: parseInt(process.env.ACLI_TIMEOUT) || 5000,

    // 输出模式配置
    modes: {
        console: {
            description: '控制台交互模式 - 支持无限次持续对话',
            emoji: '💬'
        },
        json: {
            description: 'JSON序列化模式 - 结构化数据输出',
            emoji: '📊'
        },
        stream: {
            description: '流式JSON模式 - 实时数据流处理',
            emoji: '🌊'
        }
    },

    // 预设命令
    presetCommands: {
        greeting: '你好，请介绍一下你自己',
        help: '请告诉我你能帮我做什么',
        clear: '/clear',
        status: '显示当前会话状态',
        code: {
            javascript: '帮我创建一个JavaScript项目',
            python: '帮我创建一个Python项目',
            react: '帮我创建一个React应用'
        }
    }
};

class AcliLauncher {
    constructor() {
        this.currentApi = null;
        this.mode = null;
    }

    // 显示欢迎信息
    showWelcome() {
        console.log('🤖 ACLI Rovodev 持续对话启动器');
        console.log('=' * 50);
        console.log(`工作目录: ${CONFIG.workingDirectory}`);
        console.log(`响应超时: ${CONFIG.responseTimeout}ms`);
        console.log('\n可用模式:');

        Object.entries(CONFIG.modes).forEach(([mode, config], index) => {
            console.log(`  ${index + 1}. ${config.emoji} ${mode} - ${config.description}`);
        });

        console.log('\n预设命令示例:');
        console.log('  - greeting: 问候语');
        console.log('  - help: 获取帮助');
        console.log('  - code.javascript: 创建JS项目');
        console.log('  - code.python: 创建Python项目');
        console.log('');
    }

    // 启动指定模式
    async launch(mode = 'console') {
        this.mode = mode;

        if (!CONFIG.modes[mode]) {
            throw new Error(`无效的模式: ${mode}`);
        }

        console.log(`\n🚀 启动 ${CONFIG.modes[mode].emoji} ${mode.toUpperCase()} 模式...`);
        console.log(`📝 ${CONFIG.modes[mode].description}\n`);

        switch (mode) {
            case 'console':
                await this.launchConsoleMode();
                break;
            case 'json':
                await this.launchJsonMode();
                break;
            case 'stream':
                await this.launchStreamMode();
                break;
            default:
                throw new Error(`模式 ${mode} 未实现`);
        }
    }

    // 控制台模式
    async launchConsoleMode() {
        this.currentApi = new AcliInteractive({
            workingDirectory: CONFIG.workingDirectory,
            outputMode: 'console',
            responseTimeout: CONFIG.responseTimeout
        });

        // 设置事件监听
        this.setupConsoleEvents();

        await this.currentApi.start();

        // 控制台模式会自动进入交互循环
        console.log('✅ 控制台模式已启动，可以开始对话！');
        console.log('💡 提示: 输入预设命令如 "greeting", "help" 等，或直接输入问题');
    }

    // JSON模式
    async launchJsonMode() {
        this.currentApi = new AcliJsonAPI({
            workingDirectory: CONFIG.workingDirectory,
            responseTimeout: CONFIG.responseTimeout
        });

        this.setupJsonEvents();

        await this.currentApi.start();

        console.log('✅ JSON API 模式已启动！');

        // 演示自动化对话
        await this.runJsonDemo();
    }

    // 流式模式
    async launchStreamMode() {
        this.currentApi = new AcliStreamAPI({
            workingDirectory: CONFIG.workingDirectory
        });

        this.setupStreamEvents();

        await this.currentApi.start();

        console.log('✅ Stream API 模式已启动！');

        // 演示流式处理
        await this.runStreamDemo();
    }

    // 设置控制台事件
    setupConsoleEvents() {
        this.currentApi.on('responseComplete', (data) => {
            console.log(`\n✨ 响应完成 (${data.conversationId})`);
        });

        this.currentApi.on('commandSent', (data) => {
            console.log(`📤 命令已发送: ${data.command}`);
        });
    }

    // 设置JSON事件
    setupJsonEvents() {
        this.currentApi.on('conversationComplete', (conversation) => {
            console.log('\n📊 结构化响应:');
            console.log(`  - 对话ID: ${conversation.id}`);
            console.log(`  - 内容长度: ${conversation.response.content.length} 字符`);
            console.log(`  - 工具调用: ${conversation.response.tools.length} 次`);
            console.log(`  - 文件创建: ${conversation.response.files.length} 个`);

            if (conversation.response.errors.length > 0) {
                console.log(`  - ⚠️ 错误: ${conversation.response.errors.length} 个`);
            }
        });
    }

    // 设置流式事件
    setupStreamEvents() {
        let chunkCount = 0;

        this.currentApi.on('streamChunk', (chunk) => {
            chunkCount++;
            process.stdout.write(`\r🌊 流式数据块 #${chunkCount} (${chunk.data.length} 字节)`);
        });

        this.currentApi.on('streamComplete', (stream) => {
            console.log(`\n✅ 流式完成: ${stream.chunks.length} 个数据块`);
            chunkCount = 0; // 重置计数器
        });
    }

    // JSON模式演示
    async runJsonDemo() {
        const demoCommands = [
            CONFIG.presetCommands.greeting,
            CONFIG.presetCommands.code.javascript,
            '为这个项目添加测试用例'
        ];

        console.log(`\n🎬 开始JSON模式演示 (${demoCommands.length} 个命令)...\n`);

        for (let i = 0; i < demoCommands.length; i++) {
            const command = demoCommands[i];
            console.log(`\n⏳ 执行命令 ${i + 1}/${demoCommands.length}: ${command}`);

            try {
                const result = await this.currentApi.sendCommand(command, { timeout: 30000 });
                console.log(`✅ 命令 ${i + 1} 完成`);
            } catch (error) {
                console.error(`❌ 命令 ${i + 1} 失败:`, error.message);
            }

            // 命令间隔
            if (i < demoCommands.length - 1) {
                await this.sleep(2000);
            }
        }

        // 显示统计
        const stats = this.currentApi.getStats();
        console.log('\n📈 最终统计:');
        console.log(JSON.stringify(stats, null, 2));

        console.log('\n🔄 演示完成，API仍在运行，可以继续使用...');
    }

    // 流式模式演示
    async runStreamDemo() {
        const streamCommands = [
            '创建一个完整的Web应用项目结构',
            '为项目编写详细的README文档'
        ];

        console.log(`\n🎬 开始流式模式演示 (${streamCommands.length} 个命令)...\n`);

        for (let i = 0; i < streamCommands.length; i++) {
            const command = streamCommands[i];
            console.log(`\n🌊 开始流式命令 ${i + 1}/${streamCommands.length}: ${command}`);

            try {
                const stream = await this.currentApi.sendStreamCommand(command);
                console.log(`✅ 流式命令 ${i + 1} 完成，处理了 ${stream.chunks.length} 个数据块`);
            } catch (error) {
                console.error(`❌ 流式命令 ${i + 1} 失败:`, error.message);
            }

            if (i < streamCommands.length - 1) {
                await this.sleep(3000);
            }
        }

        console.log('\n🔄 流式演示完成！');
    }

    // 获取当前API状态
    getStatus() {
        if (!this.currentApi) {
            return { status: 'not_started', mode: null };
        }

        const baseStatus = {
            status: 'running',
            mode: this.mode,
            workingDirectory: CONFIG.workingDirectory
        };

        if (this.mode === 'json') {
            return {
                ...baseStatus,
                stats: this.currentApi.getStats()
            };
        }

        return baseStatus;
    }

    // 发送命令 (适用于JSON和Stream模式)
    async sendCommand(command, options = {}) {
        if (!this.currentApi) {
            throw new Error('API未启动');
        }

        if (this.mode === 'console') {
            throw new Error('控制台模式不支持编程方式发送命令');
        }

        if (this.mode === 'json') {
            return await this.currentApi.sendCommand(command, options);
        }

        if (this.mode === 'stream') {
            return await this.currentApi.sendStreamCommand(command);
        }
    }

    // 清理资源
    async cleanup() {
        if (this.currentApi) {
            await this.currentApi.cleanup();
        }
    }

    // 工具函数
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// 命令行接口
async function main() {
    const launcher = new AcliLauncher();

    // 显示欢迎信息
    launcher.showWelcome();

    // 获取启动参数
    const mode = process.argv[2] || 'console';
    const workingDir = process.argv[3];

    if (workingDir) {
        CONFIG.workingDirectory = workingDir;
    }

    // 处理优雅退出
    process.on('SIGINT', async () => {
        console.log('\n👋 接收到退出信号，正在清理...');
        await launcher.cleanup();
        process.exit(0);
    });

    try {
        await launcher.launch(mode);
    } catch (error) {
        console.error('❌ 启动失败:', error.message);
        process.exit(1);
    }
}

// 如果直接运行
if (require.main === module) {
    main();
}

module.exports = { AcliLauncher, CONFIG };