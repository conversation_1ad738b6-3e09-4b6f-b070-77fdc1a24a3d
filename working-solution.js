// working-solution.js - 真正有效的解决方案

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🔧 ACLI Rovodev 真正有效的解决方案');
console.log('=' * 50);

async function createWorkingDirectory() {
    const workingDir = path.resolve('./test1');
    if (!fs.existsSync(workingDir)) {
        fs.mkdirSync(workingDir, { recursive: true });
        console.log(`📁 创建工作目录: ${workingDir}`);
    }
    return workingDir;
}

async function setConsoleEncoding() {
    if (process.platform === 'win32') {
        console.log('🔧 设置Windows控制台编码...');
        return new Promise((resolve) => {
            const chcpProcess = spawn('chcp', ['65001'], {
                stdio: 'inherit',
                shell: true
            });
            chcpProcess.on('close', () => {
                console.log('✅ 控制台编码已设置为UTF-8');
                resolve();
            });
            chcpProcess.on('error', () => {
                console.log('⚠️ 设置编码失败，但继续执行');
                resolve();
            });
        });
    }
}

async function startAcliWithProperEncoding() {
    const workingDir = await createWorkingDirectory();
    await setConsoleEncoding();

    console.log('\n🚀 启动ACLI Rovodev...');
    console.log(`📁 工作目录: ${workingDir}`);
    console.log('💡 如果仍有编码问题，请在PowerShell中运行此脚本\n');

    // 创建启动脚本
    const startScript = path.join(workingDir, 'start.bat');
    const scriptContent = `@echo off
chcp 65001 >nul
set PYTHONIOENCODING=utf-8
set PYTHONLEGACYWINDOWSSTDIO=1
set LANG=C.UTF-8
set LC_ALL=C.UTF-8
cd /d "${workingDir}"
acli rovodev run
pause`;

    // fs.writeFileSync(startScript, scriptContent);
    // console.log(`📝 创建启动脚本: ${startScript}`);

    // 使用PowerShell启动以确保编码正确
    const psCommand = `
        $OutputEncoding = [System.Text.Encoding]::UTF8
        [Console]::OutputEncoding = [System.Text.Encoding]::UTF8
        chcp 65001 | Out-Null
        $env:PYTHONIOENCODING = "utf-8"
        $env:PYTHONLEGACYWINDOWSSTDIO = "1"
        $env:LANG = "C.UTF-8"
        $env:LC_ALL = "C.UTF-8"
        Set-Location "${workingDir}"
        acli rovodev run
    `;

    // const psScriptPath = path.join(workingDir, 'start.ps1');
    // fs.writeFileSync(psScriptPath, psCommand);

    console.log(`📝 创建PowerShell脚本: ${psScriptPath}`);

    console.log('\n🎯 请选择启动方式:');
    console.log('1. 双击运行: ' + startScript);
    console.log('2. PowerShell运行: powershell -ExecutionPolicy Bypass -File "' + psScriptPath + '"');
    console.log('3. 手动运行: 在PowerShell中执行以下命令:');
    console.log('   chcp 65001');
    console.log('   $env:PYTHONIOENCODING = "utf-8"');
    console.log('   $env:PYTHONLEGACYWINDOWSSTDIO = "1"');
    console.log('   cd test1');
    console.log('   acli rovodev run');

    // 尝试自动启动PowerShell版本
    console.log('\n🚀 尝试自动启动PowerShell版本...');

    const psProcess = spawn('powershell', [
        '-ExecutionPolicy', 'Bypass',
        '-Command', psCommand
    ], {
        stdio: 'inherit',
        cwd: workingDir
    });

    psProcess.on('error', (error) => {
        console.error('\n❌ PowerShell启动失败:', error.message);
        console.log('\n💡 请手动运行上述命令之一');
    });

    psProcess.on('close', (code) => {
        console.log(`\n📘 进程退出，退出码: ${code}`);
    });

    // 处理Ctrl+C
    process.on('SIGINT', () => {
        console.log('\n👋 接收到退出信号...');
        psProcess.kill('SIGINT');
        process.exit(0);
    });

    return psProcess;
}

// 主函数
async function main() {
    try {
        await startAcliWithProperEncoding();
    } catch (error) {
        console.error('❌ 启动失败:', error.message);
        console.log('\n🔍 请尝试手动方式:');
        console.log('1. 打开PowerShell');
        console.log('2. 运行: chcp 65001');
        console.log('3. 设置环境变量并启动acli');
        process.exit(1);
    }
}

if (require.main === module) {
    main();
}

module.exports = { startAcliWithProperEncoding };
