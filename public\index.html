<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ACLI Rovodev 聊天界面</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="chat-container">
        <!-- 头部 -->
        <div class="chat-header">
            <div class="header-left">
                <i class="fas fa-robot"></i>
                <h1>ACLI Rovodev</h1>
                <span class="status-indicator" id="statusIndicator">
                    <i class="fas fa-circle"></i>
                    <span id="statusText">连接中...</span>
                </span>
            </div>
            <div class="header-right">
                <button class="btn btn-secondary" id="restartBtn" title="重启ACLI">
                    <i class="fas fa-redo"></i>
                </button>
                <button class="btn btn-secondary" id="clearBtn" title="清空聊天">
                    <i class="fas fa-trash"></i>
                </button>
                <button class="btn btn-secondary" id="settingsBtn" title="设置">
                    <i class="fas fa-cog"></i>
                </button>
            </div>
        </div>

        <!-- 聊天区域 -->
        <div class="chat-messages" id="chatMessages">
            <div class="welcome-message">
                <div class="message-content">
                    <i class="fas fa-robot"></i>
                    <h3>欢迎使用 ACLI Rovodev 聊天界面</h3>
                    <p>正在连接到ACLI服务器，请稍等...</p>
                    <div class="loading-dots">
                        <span></span>
                        <span></span>
                        <span></span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 输入区域 -->
        <div class="chat-input-container">
            <div class="input-wrapper">
                <textarea 
                    id="messageInput" 
                    placeholder="输入你的问题..." 
                    rows="1"
                    disabled
                ></textarea>
                <button class="send-btn" id="sendBtn" disabled>
                    <i class="fas fa-paper-plane"></i>
                </button>
            </div>
            <div class="input-footer">
                <span class="hint">按 Enter 发送，Shift+Enter 换行</span>
                <span class="char-count" id="charCount">0/1000</span>
            </div>
        </div>
    </div>

    <!-- 设置面板 -->
    <div class="settings-panel" id="settingsPanel">
        <div class="settings-content">
            <div class="settings-header">
                <h3>设置</h3>
                <button class="close-btn" id="closeSettingsBtn">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="settings-body">
                <div class="setting-item">
                    <label>自动滚动</label>
                    <input type="checkbox" id="autoScrollToggle" checked>
                </div>
                <div class="setting-item">
                    <label>显示时间戳</label>
                    <input type="checkbox" id="timestampToggle" checked>
                </div>
                <div class="setting-item">
                    <label>声音通知</label>
                    <input type="checkbox" id="soundToggle">
                </div>
                <div class="setting-item">
                    <label>主题</label>
                    <select id="themeSelect">
                        <option value="light">浅色</option>
                        <option value="dark">深色</option>
                        <option value="auto">自动</option>
                    </select>
                </div>
            </div>
        </div>
    </div>

    <!-- 连接状态提示 -->
    <div class="connection-toast" id="connectionToast">
        <i class="fas fa-wifi"></i>
        <span>连接已断开，正在重连...</span>
    </div>

    <script src="app.js"></script>
</body>
</html>
