@echo off
chcp 65001 >nul
echo 🔧 ACLI Rovodev 启动脚本 (编码修复版)
echo ==========================================

REM 设置环境变量
set PYTHONIOENCODING=utf-8
set PYTHONLEGACYWINDOWSSTDIO=1
set LANG=en_US.UTF-8
set LC_ALL=en_US.UTF-8

echo 📝 环境变量设置:
echo   PYTHONIOENCODING=%PYTHONIOENCODING%
echo   PYTHONLEGACYWINDOWSSTDIO=%PYTHONLEGACYWINDOWSSTDIO%
echo   LANG=%LANG%
echo   LC_ALL=%LC_ALL%
echo.

REM 检查工作目录
if not exist "test1" (
    echo 📁 创建工作目录: test1
    mkdir test1
)

echo 🚀 启动 acli rovodev run...
echo 💡 如果遇到编码问题，请尝试运行: node fix-encoding.js
echo.

cd test1
acli rovodev run

echo.
echo 📘 acli 进程已退出
pause
