// test-encoding.js - 编码测试脚本

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🧪 ACLI编码测试工具');
console.log('=' * 30);

class EncodingTester {
    constructor() {
        this.testResults = [];
    }

    // 测试环境变量设置
    testEnvironmentVariables() {
        console.log('\n📋 测试1: 环境变量设置');
        
        const requiredVars = {
            'PYTHONIOENCODING': 'utf-8',
            'PYTHONLEGACYWINDOWSSTDIO': '1',
            'LANG': 'C.UTF-8',
            'LC_ALL': 'C.UTF-8'
        };

        let allSet = true;
        Object.entries(requiredVars).forEach(([key, expectedValue]) => {
            const actualValue = process.env[key];
            const isSet = actualValue === expectedValue;
            
            console.log(`  ${isSet ? '✅' : '❌'} ${key}: ${actualValue || '未设置'} ${isSet ? '' : `(期望: ${expectedValue})`}`);
            
            if (!isSet) allSet = false;
        });

        this.testResults.push({
            name: '环境变量设置',
            passed: allSet,
            details: allSet ? '所有环境变量正确设置' : '部分环境变量缺失或错误'
        });

        return allSet;
    }

    // 测试工作目录
    testWorkingDirectory() {
        console.log('\n📋 测试2: 工作目录');
        
        const workingDir = path.resolve('./test1');
        const exists = fs.existsSync(workingDir);
        
        if (!exists) {
            try {
                fs.mkdirSync(workingDir, { recursive: true });
                console.log(`  ✅ 工作目录已创建: ${workingDir}`);
            } catch (error) {
                console.log(`  ❌ 无法创建工作目录: ${error.message}`);
                this.testResults.push({
                    name: '工作目录',
                    passed: false,
                    details: `无法创建目录: ${error.message}`
                });
                return false;
            }
        } else {
            console.log(`  ✅ 工作目录存在: ${workingDir}`);
        }

        this.testResults.push({
            name: '工作目录',
            passed: true,
            details: `目录路径: ${workingDir}`
        });

        return true;
    }

    // 测试acli命令可用性
    async testAcliAvailability() {
        console.log('\n📋 测试3: ACLI命令可用性');
        
        return new Promise((resolve) => {
            const testProcess = spawn('acli', ['--version'], {
                stdio: ['ignore', 'pipe', 'pipe'],
                shell: true
            });

            let stdout = '';
            let stderr = '';

            testProcess.stdout.on('data', (data) => {
                stdout += data.toString();
            });

            testProcess.stderr.on('data', (data) => {
                stderr += data.toString();
            });

            testProcess.on('close', (code) => {
                const success = code === 0;
                
                if (success) {
                    console.log(`  ✅ ACLI可用，版本信息: ${stdout.trim()}`);
                    this.testResults.push({
                        name: 'ACLI可用性',
                        passed: true,
                        details: `版本: ${stdout.trim()}`
                    });
                } else {
                    console.log(`  ❌ ACLI不可用: ${stderr || '命令未找到'}`);
                    this.testResults.push({
                        name: 'ACLI可用性',
                        passed: false,
                        details: stderr || '命令未找到'
                    });
                }
                
                resolve(success);
            });

            testProcess.on('error', (error) => {
                console.log(`  ❌ ACLI测试失败: ${error.message}`);
                this.testResults.push({
                    name: 'ACLI可用性',
                    passed: false,
                    details: error.message
                });
                resolve(false);
            });
        });
    }

    // 测试Unicode字符处理
    testUnicodeHandling() {
        console.log('\n📋 测试4: Unicode字符处理');
        
        const testChars = ['•', '→', '✅', '❌', '🚀', '中文测试'];
        let allPassed = true;

        testChars.forEach(char => {
            try {
                // 测试字符编码
                const encoded = Buffer.from(char, 'utf8');
                const decoded = encoded.toString('utf8');
                const passed = decoded === char;
                
                console.log(`  ${passed ? '✅' : '❌'} ${char} (${encoded.length} bytes)`);
                
                if (!passed) allPassed = false;
            } catch (error) {
                console.log(`  ❌ ${char} - 编码失败: ${error.message}`);
                allPassed = false;
            }
        });

        this.testResults.push({
            name: 'Unicode字符处理',
            passed: allPassed,
            details: allPassed ? '所有Unicode字符正常处理' : '部分Unicode字符处理失败'
        });

        return allPassed;
    }

    // 生成测试报告
    generateReport() {
        console.log('\n📊 测试报告');
        console.log('=' * 30);
        
        const totalTests = this.testResults.length;
        const passedTests = this.testResults.filter(test => test.passed).length;
        const failedTests = totalTests - passedTests;

        console.log(`总测试数: ${totalTests}`);
        console.log(`通过: ${passedTests} ✅`);
        console.log(`失败: ${failedTests} ❌`);
        console.log(`成功率: ${((passedTests / totalTests) * 100).toFixed(1)}%`);

        console.log('\n详细结果:');
        this.testResults.forEach((test, index) => {
            console.log(`${index + 1}. ${test.passed ? '✅' : '❌'} ${test.name}`);
            console.log(`   ${test.details}`);
        });

        // 提供建议
        console.log('\n💡 建议:');
        if (failedTests === 0) {
            console.log('✅ 所有测试通过！可以正常使用 npm run simple 启动');
        } else {
            console.log('❌ 存在问题，建议:');
            
            if (!this.testResults.find(t => t.name === 'ACLI可用性')?.passed) {
                console.log('  1. 安装 Atlassian CLI: https://developer.atlassian.com/cli/');
            }
            
            if (!this.testResults.find(t => t.name === '环境变量设置')?.passed) {
                console.log('  2. 使用 npm run simple 自动设置环境变量');
            }
            
            if (!this.testResults.find(t => t.name === 'Unicode字符处理')?.passed) {
                console.log('  3. 在PowerShell中运行: chcp 65001');
            }
        }
    }

    // 运行所有测试
    async runAllTests() {
        console.log('开始运行编码兼容性测试...\n');
        
        this.testEnvironmentVariables();
        this.testWorkingDirectory();
        await this.testAcliAvailability();
        this.testUnicodeHandling();
        
        this.generateReport();
    }
}

// 主函数
async function main() {
    const tester = new EncodingTester();
    await tester.runAllTests();
}

// 如果直接运行
if (require.main === module) {
    main().catch(error => {
        console.error('❌ 测试失败:', error.message);
        process.exit(1);
    });
}

module.exports = { EncodingTester };
