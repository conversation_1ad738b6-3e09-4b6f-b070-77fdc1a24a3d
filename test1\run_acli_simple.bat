@echo off
title ACLI Rovodev Runner
chcp 65001 >nul 2>&1

echo Setting up encoding environment...

REM Set Python encoding
set PYTHONIOENCODING=utf-8
set PYTHONUTF8=1
set PYTHON<PERSON>GACYWINDOWSSTDIO=1
set PYTHONUNBUFFERED=1

REM Set system encoding
set LANG=C.UTF-8
set LC_ALL=C.UTF-8
set LC_CTYPE=C.UTF-8

REM Disable Rich library special characters
set FORCE_COLOR=0
set NO_COLOR=1
set TERM=dumb

REM Change to working directory
cd /d "D:\test-list\rovo-run-list\test1"

echo Starting ACLI Rovodev...
echo.

REM Start ACLI and capture errors
acli rovodev run 2>error.log

REM If there are errors, show them
if exist error.log (
    echo.
    echo Error detected:
    type error.log
    del error.log
)

echo.
echo ACLI has exited
pause