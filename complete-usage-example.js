// complete-usage-example.js - 完整的使用示例展示

const { AcliInteractive } = require('./acli-rovodev-interactive-enhanced');
const { AcliJsonAPI, AcliStreamAPI } = require('./json-api-wrapper');
const fs = require('fs').promises;

// 示例1: 基本持续对话模式
async function basicPersistentMode() {
    console.log('🚀 启动基本持续对话模式...\n');

    const acli = new AcliInteractive({
        workingDirectory: 'D:\\test-list\\rovo-run-list\\test1',
        outputMode: 'console',
        responseTimeout: 5000
    });

    // 事件监听
    acli.on('responseComplete', (data) => {
        console.log(`\n✅ 对话 ${data.conversationId} 完成`);
        console.log(`响应长度: ${data.response.length} 字符`);
    });

    await acli.start();
    // 进入交互模式，支持无限次对话
}

// 示例2: JSON序列化输出模式
async function jsonSerializedMode() {
    console.log('📊 启动JSON序列化输出模式...\n');

    const api = new AcliJsonAPI({
        workingDirectory: 'D:\\test-list\\rovo-run-list\\test1',
        responseTimeout: 10000
    });

    // 监听结构化响应
    api.on('conversationComplete', async (conversation) => {
        console.log('\n📝 结构化响应数据:');
        console.log(JSON.stringify(conversation, null, 2));

        // 保存到文件
        await saveConversationToFile(conversation);
    });

    await api.start();

    // 自动化多轮对话
    const conversations = [
        {
            command: '你好，我想学习JavaScript',
            description: '初始问候和学习意向'
        },
        {
            command: '请帮我创建一个简单的待办事项应用',
            description: '请求创建应用'
        },
        {
            command: '为这个应用添加数据持久化功能',
            description: '功能增强请求'
        },
        {
            command: '帮我优化代码性能',
            description: '性能优化请求'
        }
    ];

    console.log(`开始执行 ${conversations.length} 轮对话...\n`);

    for (let i = 0; i < conversations.length; i++) {
        const conv = conversations[i];
        console.log(`\n🔄 执行对话 ${i + 1}/${conversations.length}: ${conv.description}`);

        try {
            const result = await api.sendCommand(conv.command, { timeout: 30000 });
            console.log(`✅ 对话 ${i + 1} 完成`);
            console.log(`- 内容长度: ${result.response.content.length} 字符`);
            console.log(`- 工具调用: ${result.response.tools.length} 次`);
            console.log(`- 文件创建: ${result.response.files.length} 个`);

            // 等待一段时间再进行下一轮对话
            await sleep(2000);

        } catch (error) {
            console.error(`❌ 对话 ${i + 1} 失败:`, error.message);
        }
    }

    // 显示最终统计
    const stats = api.getStats();
    console.log('\n📊 最终统计信息:');
    console.log(JSON.stringify(stats, null, 2));

    return api;
}

// 示例3: 流式JSON输出模式
async function streamJsonMode() {
    console.log('🌊 启动流式JSON输出模式...\n');

    const streamApi = new AcliStreamAPI({
        workingDirectory: 'D:\\test-list\\rovo-run-list\\test1'
    });

    let chunkCount = 0;
    const chunks = [];

    // 实时处理流式数据
    streamApi.on('streamChunk', (chunk) => {
        chunkCount++;
        chunks.push(chunk);

        // 实时输出流式数据
        process.stdout.write(`\r📦 接收数据块 #${chunkCount} (${chunk.data.length} 字节)`);

        // 可以在这里实时处理数据，比如：
        // - 实时显示进度
        // - 实时保存到数据库
        // - 实时推送到WebSocket客户端
    });

    streamApi.on('streamComplete', async (stream) => {
        console.log(`\n✅ 流式响应完成！`);
        console.log(`- 总数据块: ${stream.chunks.length}`);
        console.log(`- 总数据量: ${stream.chunks.reduce((sum, c) => sum + c.data.length, 0)} 字节`);

        // 保存完整流数据
        await saveStreamToFile(stream);
    });

    await streamApi.start();

    // 发送复杂任务，观察流式输出
    const complexTasks = [
        '创建一个完整的Node.js Web服务器，包含用户认证、数据库连接和API接口',
        '为上面的服务器编写完整的测试套件',
        '创建对应的前端React应用，包含登录、注册、数据展示等页面'
    ];

    for (const task of complexTasks) {
        console.log(`\n🚀 开始流式任务: ${task.substring(0, 50)}...`);

        try {
            const stream = await streamApi.sendStreamCommand(task);
            console.log(`✅ 任务完成，处理了 ${stream.chunks.length} 个数据块`);
        } catch (error) {
            console.error('❌ 流式任务失败:', error.message);
        }
    }

    return streamApi;
}

// 示例4: 混合模式 - 同时使用多种输出方式
async function hybridMode() {
    console.log('🔀 启动混合模式...\n');

    // 创建多个实例处理不同类型的任务
    const consoleApi = new AcliInteractive({
        workingDirectory: 'D:\\test-list\\rovo-run-list\\test1',
        outputMode: 'console'
    });

    const jsonApi = new AcliJsonAPI({
        workingDirectory: 'D:\\test-list\\rovo-run-list\\test1'
    });

    const streamApi = new AcliStreamAPI({
        workingDirectory: 'D:\\test-list\\rovo-run-list\\test1'
    });

    // 启动所有实例
    await Promise.all([
        consoleApi.start(),
        jsonApi.start(),
        streamApi.start()
    ]);

    console.log('🎯 所有API实例已启动，开始混合处理...\n');

    // 不同类型的任务使用不同的API
    const tasks = [
        {
            api: 'json',
            command: '分析一下当前目录的文件结构',
            reason: '需要结构化数据用于后续处理'
        },
        {
            api: 'stream',
            command: '创建一个大型的React应用项目结构',
            reason: '大量输出适合流式处理'
        },
        {
            api: 'console',
            command: '解释一下刚才创建的项目架构',
            reason: '交互式解释适合控制台模式'
        }
    ];

    for (const task of tasks) {
        console.log(`\n🔧 使用 ${task.api.toUpperCase()} API: ${task.reason}`);
        console.log(`📝 任务: ${task.command}`);

        try {
            let result;
            switch (task.api) {
                case 'json':
                    result = await jsonApi.sendCommand(task.command);
                    console.log('📊 JSON结果:', {
                        contentLength: result.response.content.length,
                        toolsUsed: result.response.tools.length,
                        filesCreated: result.response.files.length
                    });
                    break;

                case 'stream':
                    result = await streamApi.sendStreamCommand(task.command);
                    console.log('🌊 流式结果:', {
                        chunks: result.chunks.length,
                        totalBytes: result.chunks.reduce((sum, c) => sum + c.data.length, 0)
                    });
                    break;

                case 'console':
                    // 控制台模式需要手动交互，这里跳过
                    console.log('⏭️ 控制台模式需要手动交互，跳过自动化执行');
                    break;
            }
        } catch (error) {
            console.error(`❌ ${task.api} API 任务失败:`, error.message);
        }

        await sleep(3000); // 任务间隔
    }

    // 比较各API的性能
    console.log('\n📊 API性能对比:');
    console.log('JSON API:', jsonApi.getStats());
    console.log('Stream API: 已处理的流数据');
    console.log('Console API: 交互式会话');

    return { consoleApi, jsonApi, streamApi };
}

// 工具函数
async function saveConversationToFile(conversation) {
    const filename = `conversation_${conversation.id}.json`;
    try {
        await fs.writeFile(filename, JSON.stringify(conversation, null, 2), 'utf8');
        console.log(`💾 对话已保存到: ${filename}`);
    } catch (error) {
        console.error('保存文件失败:', error.message);
    }
}

async function saveStreamToFile(stream) {
    const filename = `stream_${stream.id}.json`;
    try {
        await fs.writeFile(filename, JSON.stringify(stream, null, 2), 'utf8');
        console.log(`💾 流数据已保存到: ${filename}`);
    } catch (error) {
        console.error('保存流文件失败:', error.message);
    }
}

function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

// 主程序入口
async function main() {
    const mode = process.argv[2] || 'basic';

    console.log('🤖 ACLI Rovodev 完整示例程序');
    console.log('=' * 60);
    console.log(`运行模式: ${mode}\n`);

    try {
        switch (mode) {
            case 'basic':
                await basicPersistentMode();
                break;

            case 'json':
                const jsonApi = await jsonSerializedMode();
                // 保持程序运行，可以继续发送命令
                console.log('\n🔄 JSON API 仍在运行，可以通过编程方式继续发送命令...');
                // 例如: await jsonApi.sendCommand('新的命令');
                break;

            case 'stream':
                const streamApi = await streamJsonMode();
                console.log('\n🔄 Stream API 已完成演示');
                break;

            case 'hybrid':
                const apis = await hybridMode();
                console.log('\n🔄 混合模式演示完成，所有API实例仍在运行');
                break;

            default:
                console.log('❌ 无效的运行模式');
                console.log('可用模式: basic, json, stream, hybrid');
                process.exit(1);
        }

    } catch (error) {
        console.error('❌ 程序执行失败:', error);
        process.exit(1);
    }
}

// 程序启动
if (require.main === module) {
    // 处理优雅退出
    process.on('SIGINT', () => {
        console.log('\n👋 接收到退出信号，正在清理资源...');
        process.exit(0);
    });

    main();
}

module.exports = {
    basicPersistentMode,
    jsonSerializedMode,
    streamJsonMode,
    hybridMode
};