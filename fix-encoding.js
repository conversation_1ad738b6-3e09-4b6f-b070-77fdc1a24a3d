// fix-encoding.js - Windows编码修复脚本

const { spawn } = require('child_process');
const path = require('path');

class EncodingFixer {
    constructor() {
        this.isWindows = process.platform === 'win32';
    }

    // 设置Windows控制台编码为UTF-8
    async setWindowsEncoding() {
        if (!this.isWindows) {
            console.log('非Windows系统，跳过编码设置');
            return true;
        }

        console.log('🔧 正在设置Windows控制台编码为UTF-8...');
        
        try {
            // 设置当前会话的代码页为UTF-8
            await this.runCommand('chcp', ['65001']);
            console.log('✅ 控制台编码已设置为UTF-8');
            return true;
        } catch (error) {
            console.warn('⚠️ 设置编码失败，但可以继续:', error.message);
            return false;
        }
    }

    // 运行命令的Promise包装
    runCommand(command, args = []) {
        return new Promise((resolve, reject) => {
            const process = spawn(command, args, {
                stdio: ['inherit', 'pipe', 'pipe'],
                shell: true
            });

            let stdout = '';
            let stderr = '';

            process.stdout.on('data', (data) => {
                stdout += data.toString();
            });

            process.stderr.on('data', (data) => {
                stderr += data.toString();
            });

            process.on('close', (code) => {
                if (code === 0) {
                    resolve(stdout);
                } else {
                    reject(new Error(`命令失败 (${code}): ${stderr || stdout}`));
                }
            });

            process.on('error', (error) => {
                reject(error);
            });
        });
    }

    // 检查acli是否可用
    async checkAcliAvailable() {
        console.log('🔍 检查acli是否可用...');
        
        try {
            await this.runCommand('acli', ['--version']);
            console.log('✅ acli命令可用');
            return true;
        } catch (error) {
            console.error('❌ acli命令不可用:', error.message);
            console.log('💡 请确保已正确安装Atlassian CLI');
            return false;
        }
    }

    // 启动修复后的acli
    async startFixedAcli(workingDir = './test1') {
        console.log('🚀 启动编码修复版本的acli...');
        
        // 确保工作目录存在
        const fs = require('fs');
        if (!fs.existsSync(workingDir)) {
            fs.mkdirSync(workingDir, { recursive: true });
            console.log(`📁 创建工作目录: ${workingDir}`);
        }

        const env = {
            ...process.env,
            // Python编码设置
            PYTHONIOENCODING: 'utf-8',
            PYTHONLEGACYWINDOWSSTDIO: '1',
            
            // 系统编码设置
            LANG: 'en_US.UTF-8',
            LC_ALL: 'en_US.UTF-8',
            
            // Windows特定设置
            CHCP: '65001',
            
            // 强制使用英文输出避免中文编码问题
            ACLI_LOCALE: 'en_US'
        };

        console.log('📝 使用的环境变量:');
        console.log('  PYTHONIOENCODING:', env.PYTHONIOENCODING);
        console.log('  PYTHONLEGACYWINDOWSSTDIO:', env.PYTHONLEGACYWINDOWSSTDIO);
        console.log('  LANG:', env.LANG);
        console.log('  LC_ALL:', env.LC_ALL);
        console.log('  CHCP:', env.CHCP);

        const acliProcess = spawn('acli', ['rovodev', 'run'], {
            cwd: path.resolve(workingDir),
            stdio: 'inherit',
            env: env,
            shell: true
        });

        acliProcess.on('error', (error) => {
            console.error('❌ 启动acli失败:', error.message);
        });

        acliProcess.on('close', (code) => {
            console.log(`📘 acli进程退出，退出码: ${code}`);
        });

        // 处理Ctrl+C
        process.on('SIGINT', () => {
            console.log('\n👋 接收到退出信号，正在关闭acli...');
            acliProcess.kill('SIGINT');
            process.exit(0);
        });

        return acliProcess;
    }
}

// 主函数
async function main() {
    console.log('🔧 ACLI编码修复工具');
    console.log('=' * 40);
    
    const fixer = new EncodingFixer();
    
    // 1. 设置Windows编码
    await fixer.setWindowsEncoding();
    
    // 2. 检查acli可用性
    const acliAvailable = await fixer.checkAcliAvailable();
    if (!acliAvailable) {
        process.exit(1);
    }
    
    // 3. 获取工作目录参数
    const workingDir = process.argv[2] || './test1';
    console.log(`📁 工作目录: ${path.resolve(workingDir)}`);
    
    // 4. 启动修复版本的acli
    console.log('\n🚀 启动acli rovodev run...');
    await fixer.startFixedAcli(workingDir);
}

// 如果直接运行
if (require.main === module) {
    main().catch(error => {
        console.error('❌ 程序执行失败:', error.message);
        process.exit(1);
    });
}

module.exports = { EncodingFixer };
